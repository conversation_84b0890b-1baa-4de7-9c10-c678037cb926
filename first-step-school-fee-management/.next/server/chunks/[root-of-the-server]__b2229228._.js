module.exports = {

"[project]/.next-internal/server/app/api/attendance/statistics/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "supabase": (()=>supabase),
    "supabaseAdmin": (()=>supabaseAdmin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-route] (ecmascript) <locals>");
;
const supabaseUrl = ("TURBOPACK compile-time value", "https://ytfzqzjuhcdgcvvqihda.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0Znpxemp1aGNkZ2N2dnFpaGRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNjM1NjMsImV4cCI6MjA1MTYzOTU2M30.rXjVX0vZwZtD83oztSpcyY6331t6aitjgsvKuTgUzfg");
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = supabaseServiceKey ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseServiceKey) : supabase // Fallback to regular client if service key is not available
;
}}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "bulkMarkAttendance": (()=>bulkMarkAttendance),
    "createFeePayment": (()=>createFeePayment),
    "getAllFeePayments": (()=>getAllFeePayments),
    "getAttendanceByDate": (()=>getAttendanceByDate),
    "getAttendanceMessages": (()=>getAttendanceMessages),
    "getAttendanceStatistics": (()=>getAttendanceStatistics),
    "getAttendanceTrends": (()=>getAttendanceTrends),
    "getClasses": (()=>getClasses),
    "getClassesWithNames": (()=>getClassesWithNames),
    "getFeePaymentByReceiptUrl": (()=>getFeePaymentByReceiptUrl),
    "getFeePaymentsByStudent": (()=>getFeePaymentsByStudent),
    "getPaymentSummary": (()=>getPaymentSummary),
    "getStudentById": (()=>getStudentById),
    "getStudentsByClass": (()=>getStudentsByClass),
    "getStudentsWithAttendanceForDate": (()=>getStudentsWithAttendanceForDate),
    "getStudentsWithPendingFees": (()=>getStudentsWithPendingFees),
    "markAttendance": (()=>markAttendance),
    "saveAttendanceMessage": (()=>saveAttendanceMessage),
    "updateFeePayment": (()=>updateFeePayment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-route] (ecmascript)");
;
async function getClassesWithNames() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('Class').select('id, name, section').order('name, section');
        if (error) throw error;
        return data || [];
    }
}
async function getClasses() {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('IDCard').select('class_id').order('class_id');
        if (error) throw error;
        // Get unique class names
        const uniqueClasses = [
            ...new Set(data.map((item)=>item.class_id).filter(Boolean))
        ];
        return uniqueClasses;
    }
}
async function getStudentsByClass(className) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('IDCard').select('*').eq('class_id', className).order('student_name');
        if (error) throw error;
        return data || [];
    }
}
async function getStudentById(studentId) {
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('IDCard').select('*').eq('id', studentId).single();
    if (error) {
        if (error.code === 'PGRST116') return null // No rows returned
        ;
        throw error;
    }
    return data;
}
async function createFeePayment(payment) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const receiptId = crypto.randomUUID();
        const receiptUrl = `/receipt/${receiptId}`;
        // Extract month and year from payment_date
        const paymentDate = new Date(payment.payment_date);
        const fee_month = paymentDate.getMonth() + 1;
        const fee_year = paymentDate.getFullYear();
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('fee_payments').insert({
            ...payment,
            receipt_url: receiptUrl,
            fee_month,
            fee_year
        }).select('*').single();
        if (error) throw error;
        return data;
    }
}
async function updateFeePayment(id, updates, updatedBy = 'system', updateReason) {
    const response = await fetch('/api/payments', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            id,
            ...updates,
            updated_by: updatedBy,
            update_reason: updateReason
        })
    });
    if (!response.ok) throw new Error('Failed to update payment');
    return response.json();
}
async function getFeePaymentByReceiptUrl(receiptUrl) {
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('fee_payments').select(`
      *,
      student:IDCard(
        *,
        class:Class(name, section)
      )
    `).eq('receipt_url', receiptUrl).single();
    if (error) {
        if (error.code === 'PGRST116') return null // No rows returned
        ;
        throw error;
    }
    return data;
}
async function getFeePaymentsByStudent(studentId) {
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('fee_payments').select('*').eq('student_id', studentId).order('payment_date', {
        ascending: false
    });
    if (error) throw error;
    return data || [];
}
async function getAllFeePayments() {
    const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('fee_payments').select(`
      *,
      student:IDCard(*)
    `).order('payment_date', {
        ascending: false
    });
    if (error) throw error;
    return data || [];
}
async function getStudentsWithPendingFees() {
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    // Get ALL students from IDCard table
    const { data: allStudents, error: studentsError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('IDCard').select(`
      *,
      class:Class(name, section)
    `);
    if (studentsError) throw studentsError;
    // Get ALL payments
    const { data: allPayments, error: paymentsError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('fee_payments').select('*');
    if (paymentsError) throw paymentsError;
    // Filter payments for current month with completed status and zero balance
    const completedPaymentsThisMonth = (allPayments || []).filter((payment)=>{
        const paymentDate = new Date(payment.payment_date);
        const paymentMonth = paymentDate.getMonth() + 1;
        const paymentYear = paymentDate.getFullYear();
        return paymentMonth === currentMonth && paymentYear === currentYear && payment.payment_status === 'completed' && payment.balance_remaining === 0;
    });
    // Create set of students who have completed payments for current month
    const studentsWithCompletedPayments = new Set(completedPaymentsThisMonth.map((payment)=>payment.student_id));
    // Group all payments by student_id
    const paymentsByStudent = new Map();
    allPayments?.forEach((payment)=>{
        if (!paymentsByStudent.has(payment.student_id)) {
            paymentsByStudent.set(payment.student_id, []);
        }
        paymentsByStudent.get(payment.student_id).push(payment);
    });
    // Return ALL students MINUS those with completed payments for current month
    const studentsWithPending = (allStudents || []).filter((student)=>!studentsWithCompletedPayments.has(student.id)).map((student)=>{
        const studentPayments = paymentsByStudent.get(student.id) || [];
        // Calculate totals from all payments
        const totalPaid = studentPayments.reduce((sum, payment)=>sum + (parseFloat(payment.amount_received) || 0), 0);
        const totalPending = studentPayments.reduce((sum, payment)=>sum + (parseFloat(payment.balance_remaining) || 0), 0);
        // Get last payment
        const lastPayment = studentPayments.sort((a, b)=>new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime())[0];
        // Check if student has payment for current month using payment_date
        const currentMonthPayment = studentPayments.find((payment)=>{
            const paymentDate = new Date(payment.payment_date);
            return paymentDate.getMonth() + 1 === currentMonth && paymentDate.getFullYear() === currentYear;
        });
        // Calculate pending amount
        let pendingAmount = totalPending;
        if (!currentMonthPayment) {
            pendingAmount += 1000 // Add default monthly fee if no payment for current month
            ;
        }
        return {
            ...student,
            totalPaid,
            totalPending: pendingAmount,
            lastPaymentDate: lastPayment?.payment_date,
            lastPaymentAmount: lastPayment ? parseFloat(lastPayment.amount_received) : null,
            pendingReason: !currentMonthPayment ? `No payment for ${currentMonth}/${currentYear}` : `Outstanding balance: ₹${totalPending}`
        };
    });
    return studentsWithPending;
}
async function getPaymentSummary() {
    const { data: payments, error: paymentsError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('fee_payments').select('amount_received, balance_remaining');
    if (paymentsError) throw paymentsError;
    const { data: students, error: studentsError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('IDCard').select('id');
    if (studentsError) throw studentsError;
    const totalCollected = payments?.reduce((sum, payment)=>sum + payment.amount_received, 0) || 0;
    const totalPending = payments?.reduce((sum, payment)=>sum + payment.balance_remaining, 0) || 0;
    // Count students with pending fees
    const studentsWithPending = await getStudentsWithPendingFees();
    return {
        totalCollected,
        totalPending,
        totalStudents: students?.length || 0,
        studentsWithPending: studentsWithPending.length
    };
}
async function markAttendance(attendanceData) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('attendance').upsert(attendanceData, {
            onConflict: 'student_id,attendance_date',
            ignoreDuplicates: false
        }).select('*').single();
        if (error) throw error;
        return data;
    }
}
async function bulkMarkAttendance(attendanceList) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('attendance').upsert(attendanceList, {
            onConflict: 'student_id,attendance_date',
            ignoreDuplicates: false
        }).select('*');
        if (error) throw error;
        return data || [];
    }
}
async function getAttendanceByDate(date) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('attendance').select(`
        *,
        student:IDCard(*)
      `).eq('attendance_date', date).order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    }
}
async function getStudentsWithAttendanceForDate(date, classId) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        let studentsQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('IDCard').select(`
        *,
        class:Class(name, section)
      `).order('student_name');
        if (classId && classId !== 'all') {
            studentsQuery = studentsQuery.eq('class_id', classId);
        }
        const { data: students, error: studentsError } = await studentsQuery;
        if (studentsError) throw studentsError;
        const { data: attendance, error: attendanceError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('attendance').select('*').eq('attendance_date', date);
        if (attendanceError) throw attendanceError;
        // Merge students with their attendance data
        const studentsWithAttendance = students?.map((student)=>{
            const studentAttendance = attendance?.find((att)=>att.student_id === student.id);
            return {
                ...student,
                attendance: studentAttendance
            };
        }) || [];
        return studentsWithAttendance;
    }
}
async function getAttendanceStatistics(date, classId) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        let studentsQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('IDCard').select('id', {
            count: 'exact'
        });
        if (classId && classId !== 'all') {
            studentsQuery = studentsQuery.eq('class_id', classId);
        }
        const { data: totalStudents, error: studentsError } = await studentsQuery;
        if (studentsError) throw studentsError;
        let attendanceQuery = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('attendance').select(`
        status,
        student:IDCard!inner(class_id)
      `).eq('attendance_date', date);
        if (classId && classId !== 'all') {
            attendanceQuery = attendanceQuery.eq('student.class_id', classId);
        }
        const { data: attendance, error: attendanceError } = await attendanceQuery;
        if (attendanceError) throw attendanceError;
        const total = totalStudents?.length || 0;
        const presentCount = attendance?.filter((att)=>att.status === 'present').length || 0;
        const absentCount = attendance?.filter((att)=>att.status === 'absent').length || 0;
        const attendancePercentage = total > 0 ? presentCount / total * 100 : 0;
        return {
            totalStudents: total,
            presentCount,
            absentCount,
            attendancePercentage: Math.round(attendancePercentage * 100) / 100
        };
    }
}
async function getAttendanceTrends(days = 30) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - days);
        const { data: attendance, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('attendance').select('attendance_date, status').gte('attendance_date', startDate.toISOString().split('T')[0]).lte('attendance_date', endDate.toISOString().split('T')[0]).order('attendance_date');
        if (error) throw error;
        const { data: totalStudents, error: studentsError } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('IDCard').select('id', {
            count: 'exact'
        });
        if (studentsError) throw studentsError;
        const total = totalStudents?.length || 0;
        // Group by date and calculate statistics
        const dateGroups = attendance?.reduce((acc, record)=>{
            const date = record.attendance_date;
            if (!acc[date]) {
                acc[date] = {
                    present: 0,
                    absent: 0
                };
            }
            if (record.status === 'present') {
                acc[date].present++;
            } else {
                acc[date].absent++;
            }
            return acc;
        }, {}) || {};
        return Object.entries(dateGroups).map(([date, counts])=>({
                date,
                presentCount: counts.present,
                absentCount: counts.absent,
                attendancePercentage: total > 0 ? Math.round(counts.present / total * 10000) / 100 : 0
            }));
    }
}
async function saveAttendanceMessage(messageData) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        const { data, error } = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('attendance_messages').insert(messageData).select('*').single();
        if (error) throw error;
        return data;
    }
}
async function getAttendanceMessages(date) {
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    } else {
        // Server-side: direct database access
        let query = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["supabase"].schema('school').from('attendance_messages').select(`
        *,
        student:IDCard(*)
      `).order('created_at', {
            ascending: false
        });
        if (date) {
            query = query.eq('attendance_date', date);
        }
        const { data, error } = await query;
        if (error) throw error;
        return data || [];
    }
}
}}),
"[project]/src/app/api/attendance/statistics/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const date = searchParams.get('date');
        const classId = searchParams.get('class');
        if (!date) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Date parameter is required'
            }, {
                status: 400
            });
        }
        const statistics = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAttendanceStatistics"])(date, classId);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(statistics);
    } catch (error) {
        console.error('Error fetching attendance statistics:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch attendance statistics'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b2229228._.js.map