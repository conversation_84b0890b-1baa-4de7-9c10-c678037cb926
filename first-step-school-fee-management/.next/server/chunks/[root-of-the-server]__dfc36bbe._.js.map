{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY\n\nif (!supabaseUrl) {\n  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')\n}\n\nif (!supabaseAnonKey) {\n  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')\n}\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// For server-side operations that require elevated permissions\nexport const supabaseAdmin = supabaseServiceKey\n  ? createClient(supabaseUrl, supabaseServiceKey)\n  : supabase // Fallback to regular client if service key is not available\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,uCAAkB;;AAElB;AAEA,uCAAsB;;AAEtB;AAEO,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,qBACzB,CAAA,GAAA,yLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,sBAC1B,SAAS,6DAA6D", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/lib/database.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport { Student, FeePayment, Attendance, AttendanceMessage } from '@/types/database'\n\n// Get class information with names\nexport async function getClassesWithNames(): Promise<{id: string, name: string, section: string}[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/classes-with-names')\n    if (!response.ok) throw new Error('Failed to fetch classes')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('Class')\n      .select('id, name, section')\n      .order('name, section')\n\n    if (error) throw error\n    return data || []\n  }\n}\n\n// Client-side functions that use API routes\nexport async function getClasses(): Promise<string[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/classes')\n    if (!response.ok) throw new Error('Failed to fetch classes')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('IDCard')\n      .select('class_id')\n      .order('class_id')\n\n    if (error) throw error\n\n    // Get unique class names\n    const uniqueClasses = [...new Set(data.map(item => item.class_id).filter(Boolean))]\n    return uniqueClasses\n  }\n}\n\nexport async function getStudentsByClass(className: string): Promise<Student[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch(`/api/students?class=${encodeURIComponent(className)}`)\n    if (!response.ok) throw new Error('Failed to fetch students')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('IDCard')\n      .select('*')\n      .eq('class_id', className)\n      .order('student_name')\n\n    if (error) throw error\n    return data || []\n  }\n}\n\nexport async function getStudentById(studentId: string): Promise<Student | null> {\n  const { data, error } = await supabase\n    .schema('school')\n    .from('IDCard')\n    .select('*')\n    .eq('id', studentId)\n    .single()\n\n  if (error) {\n    if (error.code === 'PGRST116') return null // No rows returned\n    throw error\n  }\n  return data\n}\n\n// Fee payment operations\nexport async function createFeePayment(payment: Omit<FeePayment, 'id' | 'created_at' | 'updated_at' | 'receipt_url' | 'has_updates'>): Promise<FeePayment> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/payments', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(payment),\n    })\n    if (!response.ok) throw new Error('Failed to create payment')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const receiptId = crypto.randomUUID()\n    const receiptUrl = `/receipt/${receiptId}`\n\n    // Extract month and year from payment_date\n    const paymentDate = new Date(payment.payment_date)\n    const fee_month = paymentDate.getMonth() + 1\n    const fee_year = paymentDate.getFullYear()\n\n    const { data, error } = await supabase\n      .schema('school')\n      .from('fee_payments')\n      .insert({\n        ...payment,\n        receipt_url: receiptUrl,\n        fee_month,\n        fee_year\n      })\n      .select('*')\n      .single()\n\n    if (error) throw error\n    return data\n  }\n}\n\nexport async function updateFeePayment(\n  id: string,\n  updates: Partial<FeePayment>,\n  updatedBy: string = 'system',\n  updateReason?: string\n): Promise<FeePayment> {\n  const response = await fetch('/api/payments', {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      id,\n      ...updates,\n      updated_by: updatedBy,\n      update_reason: updateReason\n    }),\n  })\n\n  if (!response.ok) throw new Error('Failed to update payment')\n  return response.json()\n}\n\nexport async function getFeePaymentByReceiptUrl(receiptUrl: string): Promise<FeePayment | null> {\n  const { data, error } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select(`\n      *,\n      student:IDCard(\n        *,\n        class:Class(name, section)\n      )\n    `)\n    .eq('receipt_url', receiptUrl)\n    .single()\n\n  if (error) {\n    if (error.code === 'PGRST116') return null // No rows returned\n    throw error\n  }\n  return data\n}\n\nexport async function getFeePaymentsByStudent(studentId: string): Promise<FeePayment[]> {\n  const { data, error } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select('*')\n    .eq('student_id', studentId)\n    .order('payment_date', { ascending: false })\n\n  if (error) throw error\n  return data || []\n}\n\nexport async function getAllFeePayments(): Promise<FeePayment[]> {\n  const { data, error } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select(`\n      *,\n      student:IDCard(*)\n    `)\n    .order('payment_date', { ascending: false })\n\n  if (error) throw error\n  return data || []\n}\n\nexport async function getStudentsWithPendingFees(): Promise<any[]> {\n  const currentMonth = new Date().getMonth() + 1\n  const currentYear = new Date().getFullYear()\n\n  // Get ALL students from IDCard table\n  const { data: allStudents, error: studentsError } = await supabase\n    .schema('school')\n    .from('IDCard')\n    .select(`\n      *,\n      class:Class(name, section)\n    `)\n\n  if (studentsError) throw studentsError\n\n  // Get ALL payments\n  const { data: allPayments, error: paymentsError } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select('*')\n\n  if (paymentsError) throw paymentsError\n\n  // Filter payments for current month with completed status and zero balance\n  const completedPaymentsThisMonth = (allPayments || []).filter(payment => {\n    const paymentDate = new Date(payment.payment_date)\n    const paymentMonth = paymentDate.getMonth() + 1\n    const paymentYear = paymentDate.getFullYear()\n\n    return paymentMonth === currentMonth &&\n           paymentYear === currentYear &&\n           payment.payment_status === 'completed' &&\n           payment.balance_remaining === 0\n  })\n\n  // Create set of students who have completed payments for current month\n  const studentsWithCompletedPayments = new Set(\n    completedPaymentsThisMonth.map(payment => payment.student_id)\n  )\n\n  // Group all payments by student_id\n  const paymentsByStudent = new Map()\n  allPayments?.forEach(payment => {\n    if (!paymentsByStudent.has(payment.student_id)) {\n      paymentsByStudent.set(payment.student_id, [])\n    }\n    paymentsByStudent.get(payment.student_id).push(payment)\n  })\n\n  // Return ALL students MINUS those with completed payments for current month\n  const studentsWithPending = (allStudents || [])\n    .filter(student => !studentsWithCompletedPayments.has(student.id))\n    .map(student => {\n      const studentPayments = paymentsByStudent.get(student.id) || []\n\n      // Calculate totals from all payments\n      const totalPaid = studentPayments.reduce((sum: number, payment: any) =>\n        sum + (parseFloat(payment.amount_received) || 0), 0)\n\n      const totalPending = studentPayments.reduce((sum: number, payment: any) =>\n        sum + (parseFloat(payment.balance_remaining) || 0), 0)\n\n      // Get last payment\n      const lastPayment = studentPayments.sort((a: any, b: any) =>\n        new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime()\n      )[0]\n\n      // Check if student has payment for current month using payment_date\n      const currentMonthPayment = studentPayments.find((payment: any) => {\n        const paymentDate = new Date(payment.payment_date)\n        return paymentDate.getMonth() + 1 === currentMonth &&\n               paymentDate.getFullYear() === currentYear\n      })\n\n      // Calculate pending amount\n      let pendingAmount = totalPending\n      if (!currentMonthPayment) {\n        pendingAmount += 1000 // Add default monthly fee if no payment for current month\n      }\n\n      return {\n        ...student,\n        totalPaid,\n        totalPending: pendingAmount,\n        lastPaymentDate: lastPayment?.payment_date,\n        lastPaymentAmount: lastPayment ? parseFloat(lastPayment.amount_received) : null,\n        pendingReason: !currentMonthPayment\n          ? `No payment for ${currentMonth}/${currentYear}`\n          : `Outstanding balance: ₹${totalPending}`\n      }\n    })\n\n  return studentsWithPending\n}\n\nexport async function getPaymentSummary(): Promise<{\n  totalCollected: number\n  totalPending: number\n  totalStudents: number\n  studentsWithPending: number\n}> {\n  const { data: payments, error: paymentsError } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select('amount_received, balance_remaining')\n\n  if (paymentsError) throw paymentsError\n\n  const { data: students, error: studentsError } = await supabase\n    .schema('school')\n    .from('IDCard')\n    .select('id')\n\n  if (studentsError) throw studentsError\n\n  const totalCollected = payments?.reduce((sum, payment) => sum + payment.amount_received, 0) || 0\n  const totalPending = payments?.reduce((sum, payment) => sum + payment.balance_remaining, 0) || 0\n\n  // Count students with pending fees\n  const studentsWithPending = await getStudentsWithPendingFees()\n\n  return {\n    totalCollected,\n    totalPending,\n    totalStudents: students?.length || 0,\n    studentsWithPending: studentsWithPending.length\n  }\n}\n\n// Attendance operations\nexport async function markAttendance(attendanceData: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>): Promise<Attendance> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/attendance', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(attendanceData),\n    })\n    if (!response.ok) throw new Error('Failed to mark attendance')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('attendance')\n      .upsert(attendanceData, {\n        onConflict: 'student_id,attendance_date',\n        ignoreDuplicates: false\n      })\n      .select('*')\n      .single()\n\n    if (error) throw error\n    return data\n  }\n}\n\nexport async function bulkMarkAttendance(attendanceList: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>[]): Promise<Attendance[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/attendance/bulk', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ attendanceList }),\n    })\n    if (!response.ok) throw new Error('Failed to mark bulk attendance')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('attendance')\n      .upsert(attendanceList, {\n        onConflict: 'student_id,attendance_date',\n        ignoreDuplicates: false\n      })\n      .select('*')\n\n    if (error) throw error\n    return data || []\n  }\n}\n\nexport async function getAttendanceByDate(date: string): Promise<Attendance[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch(`/api/attendance?date=${encodeURIComponent(date)}`)\n    if (!response.ok) throw new Error('Failed to fetch attendance')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('attendance')\n      .select(`\n        *,\n        student:IDCard(*)\n      `)\n      .eq('attendance_date', date)\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return data || []\n  }\n}\n\nexport async function getStudentsWithAttendanceForDate(date: string, classId?: string | null): Promise<(Student & { attendance?: Attendance })[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    let url = `/api/attendance/students?date=${encodeURIComponent(date)}`\n    if (classId) {\n      url += `&class=${encodeURIComponent(classId)}`\n    }\n    const response = await fetch(url)\n    if (!response.ok) throw new Error('Failed to fetch students with attendance')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    let studentsQuery = supabase\n      .schema('school')\n      .from('IDCard')\n      .select(`\n        *,\n        class:Class(name, section)\n      `)\n      .order('student_name')\n\n    if (classId && classId !== 'all') {\n      studentsQuery = studentsQuery.eq('class_id', classId)\n    }\n\n    const { data: students, error: studentsError } = await studentsQuery\n\n    if (studentsError) throw studentsError\n\n    const { data: attendance, error: attendanceError } = await supabase\n      .schema('school')\n      .from('attendance')\n      .select('*')\n      .eq('attendance_date', date)\n\n    if (attendanceError) throw attendanceError\n\n    // Merge students with their attendance data\n    const studentsWithAttendance = students?.map(student => {\n      const studentAttendance = attendance?.find(att => att.student_id === student.id)\n      return {\n        ...student,\n        attendance: studentAttendance\n      }\n    }) || []\n\n    return studentsWithAttendance\n  }\n}\n\nexport async function getAttendanceStatistics(date: string, classId?: string | null): Promise<{\n  totalStudents: number\n  presentCount: number\n  absentCount: number\n  attendancePercentage: number\n}> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    let url = `/api/attendance/statistics?date=${encodeURIComponent(date)}`\n    if (classId) {\n      url += `&class=${encodeURIComponent(classId)}`\n    }\n    const response = await fetch(url)\n    if (!response.ok) throw new Error('Failed to fetch attendance statistics')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    let studentsQuery = supabase\n      .schema('school')\n      .from('IDCard')\n      .select('id', { count: 'exact' })\n\n    if (classId && classId !== 'all') {\n      studentsQuery = studentsQuery.eq('class_id', classId)\n    }\n\n    const { data: totalStudents, error: studentsError } = await studentsQuery\n\n    if (studentsError) throw studentsError\n\n    let attendanceQuery = supabase\n      .schema('school')\n      .from('attendance')\n      .select(`\n        status,\n        student:IDCard!inner(class_id)\n      `)\n      .eq('attendance_date', date)\n\n    if (classId && classId !== 'all') {\n      attendanceQuery = attendanceQuery.eq('student.class_id', classId)\n    }\n\n    const { data: attendance, error: attendanceError } = await attendanceQuery\n\n    if (attendanceError) throw attendanceError\n\n    const total = totalStudents?.length || 0\n    const presentCount = attendance?.filter(att => att.status === 'present').length || 0\n    const absentCount = attendance?.filter(att => att.status === 'absent').length || 0\n    const attendancePercentage = total > 0 ? (presentCount / total) * 100 : 0\n\n    return {\n      totalStudents: total,\n      presentCount,\n      absentCount,\n      attendancePercentage: Math.round(attendancePercentage * 100) / 100\n    }\n  }\n}\n\nexport async function getAttendanceTrends(days: number = 30): Promise<{\n  date: string\n  presentCount: number\n  absentCount: number\n  attendancePercentage: number\n}[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch(`/api/attendance/trends?days=${days}`)\n    if (!response.ok) throw new Error('Failed to fetch attendance trends')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const endDate = new Date()\n    const startDate = new Date()\n    startDate.setDate(endDate.getDate() - days)\n\n    const { data: attendance, error } = await supabase\n      .schema('school')\n      .from('attendance')\n      .select('attendance_date, status')\n      .gte('attendance_date', startDate.toISOString().split('T')[0])\n      .lte('attendance_date', endDate.toISOString().split('T')[0])\n      .order('attendance_date')\n\n    if (error) throw error\n\n    const { data: totalStudents, error: studentsError } = await supabase\n      .schema('school')\n      .from('IDCard')\n      .select('id', { count: 'exact' })\n\n    if (studentsError) throw studentsError\n\n    const total = totalStudents?.length || 0\n\n    // Group by date and calculate statistics\n    const dateGroups = attendance?.reduce((acc, record) => {\n      const date = record.attendance_date\n      if (!acc[date]) {\n        acc[date] = { present: 0, absent: 0 }\n      }\n      if (record.status === 'present') {\n        acc[date].present++\n      } else {\n        acc[date].absent++\n      }\n      return acc\n    }, {} as Record<string, { present: number; absent: number }>) || {}\n\n    return Object.entries(dateGroups).map(([date, counts]) => ({\n      date,\n      presentCount: counts.present,\n      absentCount: counts.absent,\n      attendancePercentage: total > 0 ? Math.round((counts.present / total) * 10000) / 100 : 0\n    }))\n  }\n}\n\n// Attendance messaging operations\nexport async function saveAttendanceMessage(messageData: Omit<AttendanceMessage, 'id' | 'created_at' | 'sent_at'>): Promise<AttendanceMessage> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/attendance/messages', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(messageData),\n    })\n    if (!response.ok) throw new Error('Failed to save attendance message')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('attendance_messages')\n      .insert(messageData)\n      .select('*')\n      .single()\n\n    if (error) throw error\n    return data\n  }\n}\n\nexport async function getAttendanceMessages(date?: string): Promise<AttendanceMessage[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const url = date ? `/api/attendance/messages?date=${encodeURIComponent(date)}` : '/api/attendance/messages'\n    const response = await fetch(url)\n    if (!response.ok) throw new Error('Failed to fetch attendance messages')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    let query = supabase\n      .schema('school')\n      .from('attendance_messages')\n      .select(`\n        *,\n        student:IDCard(*)\n      `)\n      .order('created_at', { ascending: false })\n\n    if (date) {\n      query = query.eq('attendance_date', date)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n    return data || []\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;AAIO,eAAe;IACpB,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,SACL,MAAM,CAAC,qBACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAGO,eAAe;IACpB,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,YACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QAEjB,yBAAyB;QACzB,MAAM,gBAAgB;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM,CAAC;SAAU;QACnF,OAAO;IACT;AACF;AAEO,eAAe,mBAAmB,SAAiB;IACxD,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,WACf,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAEO,eAAe,eAAe,SAAiB;IACpD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,MAAM;IAET,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,KAAK,mBAAmB;;QAC9D,MAAM;IACR;IACA,OAAO;AACT;AAGO,eAAe,iBAAiB,OAA6F;IAClI,uCAAmC;;IAWnC,OAAO;QACL,sCAAsC;QACtC,MAAM,YAAY,OAAO,UAAU;QACnC,MAAM,aAAa,CAAC,SAAS,EAAE,WAAW;QAE1C,2CAA2C;QAC3C,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;QACjD,MAAM,YAAY,YAAY,QAAQ,KAAK;QAC3C,MAAM,WAAW,YAAY,WAAW;QAExC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC;YACN,GAAG,OAAO;YACV,aAAa;YACb;YACA;QACF,GACC,MAAM,CAAC,KACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAEO,eAAe,iBACpB,EAAU,EACV,OAA4B,EAC5B,YAAoB,QAAQ,EAC5B,YAAqB;IAErB,MAAM,WAAW,MAAM,MAAM,iBAAiB;QAC5C,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB;YACA,GAAG,OAAO;YACV,YAAY;YACZ,eAAe;QACjB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;IAClC,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,0BAA0B,UAAkB;IAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;;IAMT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,MAAM;IAET,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,KAAK,mBAAmB;;QAC9D,MAAM;IACR;IACA,OAAO;AACT;AAEO,eAAe,wBAAwB,SAAiB;IAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,gBAAgB;QAAE,WAAW;IAAM;IAE5C,IAAI,OAAO,MAAM;IACjB,OAAO,QAAQ,EAAE;AACnB;AAEO,eAAe;IACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,KAAK,CAAC,gBAAgB;QAAE,WAAW;IAAM;IAE5C,IAAI,OAAO,MAAM;IACjB,OAAO,QAAQ,EAAE;AACnB;AAEO,eAAe;IACpB,MAAM,eAAe,IAAI,OAAO,QAAQ,KAAK;IAC7C,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qCAAqC;IACrC,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC/D,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;IAGT,CAAC;IAEH,IAAI,eAAe,MAAM;IAEzB,mBAAmB;IACnB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC/D,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC;IAEV,IAAI,eAAe,MAAM;IAEzB,2EAA2E;IAC3E,MAAM,6BAA6B,CAAC,eAAe,EAAE,EAAE,MAAM,CAAC,CAAA;QAC5D,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;QACjD,MAAM,eAAe,YAAY,QAAQ,KAAK;QAC9C,MAAM,cAAc,YAAY,WAAW;QAE3C,OAAO,iBAAiB,gBACjB,gBAAgB,eAChB,QAAQ,cAAc,KAAK,eAC3B,QAAQ,iBAAiB,KAAK;IACvC;IAEA,uEAAuE;IACvE,MAAM,gCAAgC,IAAI,IACxC,2BAA2B,GAAG,CAAC,CAAA,UAAW,QAAQ,UAAU;IAG9D,mCAAmC;IACnC,MAAM,oBAAoB,IAAI;IAC9B,aAAa,QAAQ,CAAA;QACnB,IAAI,CAAC,kBAAkB,GAAG,CAAC,QAAQ,UAAU,GAAG;YAC9C,kBAAkB,GAAG,CAAC,QAAQ,UAAU,EAAE,EAAE;QAC9C;QACA,kBAAkB,GAAG,CAAC,QAAQ,UAAU,EAAE,IAAI,CAAC;IACjD;IAEA,4EAA4E;IAC5E,MAAM,sBAAsB,CAAC,eAAe,EAAE,EAC3C,MAAM,CAAC,CAAA,UAAW,CAAC,8BAA8B,GAAG,CAAC,QAAQ,EAAE,GAC/D,GAAG,CAAC,CAAA;QACH,MAAM,kBAAkB,kBAAkB,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE;QAE/D,qCAAqC;QACrC,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAC,KAAa,UACrD,MAAM,CAAC,WAAW,QAAQ,eAAe,KAAK,CAAC,GAAG;QAEpD,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAC,KAAa,UACxD,MAAM,CAAC,WAAW,QAAQ,iBAAiB,KAAK,CAAC,GAAG;QAEtD,mBAAmB;QACnB,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAC,GAAQ,IAChD,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,GACtE,CAAC,EAAE;QAEJ,oEAAoE;QACpE,MAAM,sBAAsB,gBAAgB,IAAI,CAAC,CAAC;YAChD,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;YACjD,OAAO,YAAY,QAAQ,KAAK,MAAM,gBAC/B,YAAY,WAAW,OAAO;QACvC;QAEA,2BAA2B;QAC3B,IAAI,gBAAgB;QACpB,IAAI,CAAC,qBAAqB;YACxB,iBAAiB,KAAK,0DAA0D;;QAClF;QAEA,OAAO;YACL,GAAG,OAAO;YACV;YACA,cAAc;YACd,iBAAiB,aAAa;YAC9B,mBAAmB,cAAc,WAAW,YAAY,eAAe,IAAI;YAC3E,eAAe,CAAC,sBACZ,CAAC,eAAe,EAAE,aAAa,CAAC,EAAE,aAAa,GAC/C,CAAC,sBAAsB,EAAE,cAAc;QAC7C;IACF;IAEF,OAAO;AACT;AAEO,eAAe;IAMpB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC5D,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC;IAEV,IAAI,eAAe,MAAM;IAEzB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC5D,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC;IAEV,IAAI,eAAe,MAAM;IAEzB,MAAM,iBAAiB,UAAU,OAAO,CAAC,KAAK,UAAY,MAAM,QAAQ,eAAe,EAAE,MAAM;IAC/F,MAAM,eAAe,UAAU,OAAO,CAAC,KAAK,UAAY,MAAM,QAAQ,iBAAiB,EAAE,MAAM;IAE/F,mCAAmC;IACnC,MAAM,sBAAsB,MAAM;IAElC,OAAO;QACL;QACA;QACA,eAAe,UAAU,UAAU;QACnC,qBAAqB,oBAAoB,MAAM;IACjD;AACF;AAGO,eAAe,eAAe,cAAoE;IACvG,uCAAmC;;IAWnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,gBAAgB;YACtB,YAAY;YACZ,kBAAkB;QACpB,GACC,MAAM,CAAC,KACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAEO,eAAe,mBAAmB,cAAsE;IAC7G,uCAAmC;;IAWnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,gBAAgB;YACtB,YAAY;YACZ,kBAAkB;QACpB,GACC,MAAM,CAAC;QAEV,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAEO,eAAe,oBAAoB,IAAY;IACpD,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,mBAAmB,MACtB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAEO,eAAe,iCAAiC,IAAY,EAAE,OAAuB;IAC1F,uCAAmC;;IASnC,OAAO;QACL,sCAAsC;QACtC,IAAI,gBAAgB,wHAAA,CAAA,WAAQ,CACzB,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC;QAET,IAAI,WAAW,YAAY,OAAO;YAChC,gBAAgB,cAAc,EAAE,CAAC,YAAY;QAC/C;QAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM;QAEvD,IAAI,eAAe,MAAM;QAEzB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAChE,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,mBAAmB;QAEzB,IAAI,iBAAiB,MAAM;QAE3B,4CAA4C;QAC5C,MAAM,yBAAyB,UAAU,IAAI,CAAA;YAC3C,MAAM,oBAAoB,YAAY,KAAK,CAAA,MAAO,IAAI,UAAU,KAAK,QAAQ,EAAE;YAC/E,OAAO;gBACL,GAAG,OAAO;gBACV,YAAY;YACd;QACF,MAAM,EAAE;QAER,OAAO;IACT;AACF;AAEO,eAAe,wBAAwB,IAAY,EAAE,OAAuB;IAMjF,uCAAmC;;IASnC,OAAO;QACL,sCAAsC;QACtC,IAAI,gBAAgB,wHAAA,CAAA,WAAQ,CACzB,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,MAAM;YAAE,OAAO;QAAQ;QAEjC,IAAI,WAAW,YAAY,OAAO;YAChC,gBAAgB,cAAc,EAAE,CAAC,YAAY;QAC/C;QAEA,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM;QAE5D,IAAI,eAAe,MAAM;QAEzB,IAAI,kBAAkB,wHAAA,CAAA,WAAQ,CAC3B,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,mBAAmB;QAEzB,IAAI,WAAW,YAAY,OAAO;YAChC,kBAAkB,gBAAgB,EAAE,CAAC,oBAAoB;QAC3D;QAEA,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM;QAE3D,IAAI,iBAAiB,MAAM;QAE3B,MAAM,QAAQ,eAAe,UAAU;QACvC,MAAM,eAAe,YAAY,OAAO,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,UAAU;QACnF,MAAM,cAAc,YAAY,OAAO,CAAA,MAAO,IAAI,MAAM,KAAK,UAAU,UAAU;QACjF,MAAM,uBAAuB,QAAQ,IAAI,AAAC,eAAe,QAAS,MAAM;QAExE,OAAO;YACL,eAAe;YACf;YACA;YACA,sBAAsB,KAAK,KAAK,CAAC,uBAAuB,OAAO;QACjE;IACF;AACF;AAEO,eAAe,oBAAoB,OAAe,EAAE;IAMzD,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,UAAU,IAAI;QACpB,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,QAAQ,OAAO,KAAK;QAEtC,MAAM,EAAE,MAAM,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC/C,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,2BACP,GAAG,CAAC,mBAAmB,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAC5D,GAAG,CAAC,mBAAmB,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAC1D,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QAEjB,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACjE,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,MAAM;YAAE,OAAO;QAAQ;QAEjC,IAAI,eAAe,MAAM;QAEzB,MAAM,QAAQ,eAAe,UAAU;QAEvC,yCAAyC;QACzC,MAAM,aAAa,YAAY,OAAO,CAAC,KAAK;YAC1C,MAAM,OAAO,OAAO,eAAe;YACnC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;gBACd,GAAG,CAAC,KAAK,GAAG;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;YACtC;YACA,IAAI,OAAO,MAAM,KAAK,WAAW;gBAC/B,GAAG,CAAC,KAAK,CAAC,OAAO;YACnB,OAAO;gBACL,GAAG,CAAC,KAAK,CAAC,MAAM;YAClB;YACA,OAAO;QACT,GAAG,CAAC,MAA6D,CAAC;QAElE,OAAO,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,GAAK,CAAC;gBACzD;gBACA,cAAc,OAAO,OAAO;gBAC5B,aAAa,OAAO,MAAM;gBAC1B,sBAAsB,QAAQ,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,OAAO,GAAG,QAAS,SAAS,MAAM;YACzF,CAAC;IACH;AACF;AAGO,eAAe,sBAAsB,WAAqE;IAC/G,uCAAmC;;IAWnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,uBACL,MAAM,CAAC,aACP,MAAM,CAAC,KACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAEO,eAAe,sBAAsB,IAAa;IACvD,uCAAmC;;IAMnC,OAAO;QACL,sCAAsC;QACtC,IAAI,QAAQ,wHAAA,CAAA,WAAQ,CACjB,MAAM,CAAC,UACP,IAAI,CAAC,uBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,MAAM;YACR,QAAQ,MAAM,EAAE,CAAC,mBAAmB;QACtC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/app/api/pending-fees/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getStudentsWithPendingFees, getPaymentSummary } from '@/lib/database'\nimport { supabase } from '@/lib/supabase'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const summary = searchParams.get('summary') === 'true'\n    const month = searchParams.get('month')\n    const year = searchParams.get('year')\n\n    if (summary) {\n      const summaryData = await getPaymentSummary()\n      return NextResponse.json(summaryData)\n    } else if (month && year) {\n      // Get students with pending fees for specific month/year\n      const pendingStudents = await getStudentsWithPendingFeesForMonth(parseInt(month), parseInt(year))\n      return NextResponse.json(pendingStudents)\n    } else {\n      const pendingStudents = await getStudentsWithPendingFees()\n      return NextResponse.json(pendingStudents)\n    }\n  } catch (error) {\n    console.error('Error fetching pending fees:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch pending fees' },\n      { status: 500 }\n    )\n  }\n}\n\nasync function getStudentsWithPendingFeesForMonth(month: number, year: number) {\n  // Get ALL students from IDCard table\n  const { data: allStudents, error: studentsError } = await supabase\n    .schema('school')\n    .from('IDCard')\n    .select(`\n      *,\n      class:Class(name, section)\n    `)\n\n  if (studentsError) throw studentsError\n\n  // Get ALL payments\n  const { data: allPayments, error: paymentsError } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select('*')\n\n  if (paymentsError) throw paymentsError\n\n  // Filter payments for the specific month/year with completed status and zero balance\n  const completedPaymentsThisMonth = (allPayments || []).filter(payment => {\n    const paymentDate = new Date(payment.payment_date)\n    const paymentMonth = paymentDate.getMonth() + 1\n    const paymentYear = paymentDate.getFullYear()\n\n    return paymentMonth === month &&\n           paymentYear === year &&\n           payment.payment_status === 'completed' &&\n           payment.balance_remaining === 0\n  })\n\n  // Create set of students who have completed payments for this month\n  const studentsWithCompletedPayments = new Set(\n    completedPaymentsThisMonth.map(payment => payment.student_id)\n  )\n\n  // Group all payments by student_id\n  const paymentsByStudent = new Map()\n  allPayments?.forEach(payment => {\n    if (!paymentsByStudent.has(payment.student_id)) {\n      paymentsByStudent.set(payment.student_id, [])\n    }\n    paymentsByStudent.get(payment.student_id).push(payment)\n  })\n\n  // Return ALL students MINUS those with completed payments for this specific month\n  const pendingStudents = (allStudents || [])\n    .filter(student => !studentsWithCompletedPayments.has(student.id))\n    .map(student => {\n      const studentPayments = paymentsByStudent.get(student.id) || []\n\n      // Get payment for this specific month using payment_date\n      const monthPayment = studentPayments.find((payment: any) => {\n        const paymentDate = new Date(payment.payment_date)\n        return paymentDate.getMonth() + 1 === month &&\n               paymentDate.getFullYear() === year\n      })\n\n      // Calculate totals from all payments\n      const totalPaid = studentPayments.reduce((sum: number, payment: { amount_received: string | number }) =>\n        sum + (parseFloat(payment.amount_received.toString()) || 0), 0)\n\n      // Get last payment\n      const lastPayment = studentPayments.sort((a: { payment_date: string }, b: { payment_date: string }) =>\n        new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime()\n      )[0]\n\n      return {\n        ...student,\n        totalPaid,\n        totalPending: monthPayment ? parseFloat(monthPayment.balance_remaining) : 1000,\n        lastPaymentDate: lastPayment?.payment_date,\n        lastPaymentAmount: lastPayment ? parseFloat(lastPayment.amount_received) : null,\n        pendingMonth: month,\n        pendingYear: year,\n        pendingReason: !monthPayment\n          ? `No payment record for ${month}/${year}`\n          : `Outstanding balance: ₹${monthPayment.balance_remaining}`\n      }\n    })\n\n  return pendingStudents\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC,eAAe;QAChD,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,IAAI,SAAS;YACX,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,oBAAiB,AAAD;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,OAAO,IAAI,SAAS,MAAM;YACxB,yDAAyD;YACzD,MAAM,kBAAkB,MAAM,mCAAmC,SAAS,QAAQ,SAAS;YAC3F,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,OAAO;YACL,MAAM,kBAAkB,MAAM,CAAA,GAAA,wHAAA,CAAA,6BAA0B,AAAD;YACvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA+B,GACxC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,mCAAmC,KAAa,EAAE,IAAY;IAC3E,qCAAqC;IACrC,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC/D,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;IAGT,CAAC;IAEH,IAAI,eAAe,MAAM;IAEzB,mBAAmB;IACnB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,wHAAA,CAAA,WAAQ,CAC/D,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC;IAEV,IAAI,eAAe,MAAM;IAEzB,qFAAqF;IACrF,MAAM,6BAA6B,CAAC,eAAe,EAAE,EAAE,MAAM,CAAC,CAAA;QAC5D,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;QACjD,MAAM,eAAe,YAAY,QAAQ,KAAK;QAC9C,MAAM,cAAc,YAAY,WAAW;QAE3C,OAAO,iBAAiB,SACjB,gBAAgB,QAChB,QAAQ,cAAc,KAAK,eAC3B,QAAQ,iBAAiB,KAAK;IACvC;IAEA,oEAAoE;IACpE,MAAM,gCAAgC,IAAI,IACxC,2BAA2B,GAAG,CAAC,CAAA,UAAW,QAAQ,UAAU;IAG9D,mCAAmC;IACnC,MAAM,oBAAoB,IAAI;IAC9B,aAAa,QAAQ,CAAA;QACnB,IAAI,CAAC,kBAAkB,GAAG,CAAC,QAAQ,UAAU,GAAG;YAC9C,kBAAkB,GAAG,CAAC,QAAQ,UAAU,EAAE,EAAE;QAC9C;QACA,kBAAkB,GAAG,CAAC,QAAQ,UAAU,EAAE,IAAI,CAAC;IACjD;IAEA,kFAAkF;IAClF,MAAM,kBAAkB,CAAC,eAAe,EAAE,EACvC,MAAM,CAAC,CAAA,UAAW,CAAC,8BAA8B,GAAG,CAAC,QAAQ,EAAE,GAC/D,GAAG,CAAC,CAAA;QACH,MAAM,kBAAkB,kBAAkB,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE;QAE/D,yDAAyD;QACzD,MAAM,eAAe,gBAAgB,IAAI,CAAC,CAAC;YACzC,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;YACjD,OAAO,YAAY,QAAQ,KAAK,MAAM,SAC/B,YAAY,WAAW,OAAO;QACvC;QAEA,qCAAqC;QACrC,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAC,KAAa,UACrD,MAAM,CAAC,WAAW,QAAQ,eAAe,CAAC,QAAQ,OAAO,CAAC,GAAG;QAE/D,mBAAmB;QACnB,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAC,GAA6B,IACrE,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,GACtE,CAAC,EAAE;QAEJ,OAAO;YACL,GAAG,OAAO;YACV;YACA,cAAc,eAAe,WAAW,aAAa,iBAAiB,IAAI;YAC1E,iBAAiB,aAAa;YAC9B,mBAAmB,cAAc,WAAW,YAAY,eAAe,IAAI;YAC3E,cAAc;YACd,aAAa;YACb,eAAe,CAAC,eACZ,CAAC,sBAAsB,EAAE,MAAM,CAAC,EAAE,MAAM,GACxC,CAAC,sBAAsB,EAAE,aAAa,iBAAiB,EAAE;QAC/D;IACF;IAEF,OAAO;AACT", "debugId": null}}]}