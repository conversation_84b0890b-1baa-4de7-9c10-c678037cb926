{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY\n\nif (!supabaseUrl) {\n  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')\n}\n\nif (!supabaseAnonKey) {\n  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')\n}\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// For server-side operations that require elevated permissions\nexport const supabaseAdmin = supabaseServiceKey\n  ? createClient(supabaseUrl, supabaseServiceKey)\n  : supabase // Fallback to regular client if service key is not available\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,qBAAqB,QAAQ,GAAG,CAAC,yBAAyB;AAEhE,uCAAkB;;AAElB;AAEA,uCAAsB;;AAEtB;AAEO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,gBAAgB,qBACzB,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,sBAC1B,SAAS,6DAA6D", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/lib/database.ts"], "sourcesContent": ["import { supabase } from './supabase'\nimport { Student, FeePayment, Attendance, AttendanceMessage } from '@/types/database'\n\n// Get class information with names\nexport async function getClassesWithNames(): Promise<{id: string, name: string, section: string}[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/classes-with-names')\n    if (!response.ok) throw new Error('Failed to fetch classes')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('Class')\n      .select('id, name, section')\n      .order('name, section')\n\n    if (error) throw error\n    return data || []\n  }\n}\n\n// Client-side functions that use API routes\nexport async function getClasses(): Promise<string[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/classes')\n    if (!response.ok) throw new Error('Failed to fetch classes')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('IDCard')\n      .select('class_id')\n      .order('class_id')\n\n    if (error) throw error\n\n    // Get unique class names\n    const uniqueClasses = [...new Set(data.map(item => item.class_id).filter(Boolean))]\n    return uniqueClasses\n  }\n}\n\nexport async function getStudentsByClass(className: string): Promise<Student[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch(`/api/students?class=${encodeURIComponent(className)}`)\n    if (!response.ok) throw new Error('Failed to fetch students')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('IDCard')\n      .select('*')\n      .eq('class_id', className)\n      .order('student_name')\n\n    if (error) throw error\n    return data || []\n  }\n}\n\nexport async function getStudentById(studentId: string): Promise<Student | null> {\n  const { data, error } = await supabase\n    .schema('school')\n    .from('IDCard')\n    .select('*')\n    .eq('id', studentId)\n    .single()\n\n  if (error) {\n    if (error.code === 'PGRST116') return null // No rows returned\n    throw error\n  }\n  return data\n}\n\n// Fee payment operations\nexport async function createFeePayment(payment: Omit<FeePayment, 'id' | 'created_at' | 'updated_at' | 'receipt_url' | 'has_updates'>): Promise<FeePayment> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/payments', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(payment),\n    })\n    if (!response.ok) throw new Error('Failed to create payment')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const receiptId = crypto.randomUUID()\n    const receiptUrl = `/receipt/${receiptId}`\n\n    // Extract month and year from payment_date\n    const paymentDate = new Date(payment.payment_date)\n    const fee_month = paymentDate.getMonth() + 1\n    const fee_year = paymentDate.getFullYear()\n\n    const { data, error } = await supabase\n      .schema('school')\n      .from('fee_payments')\n      .insert({\n        ...payment,\n        receipt_url: receiptUrl,\n        fee_month,\n        fee_year\n      })\n      .select('*')\n      .single()\n\n    if (error) throw error\n    return data\n  }\n}\n\nexport async function updateFeePayment(\n  id: string,\n  updates: Partial<FeePayment>,\n  updatedBy: string = 'system',\n  updateReason?: string\n): Promise<FeePayment> {\n  const response = await fetch('/api/payments', {\n    method: 'PUT',\n    headers: {\n      'Content-Type': 'application/json',\n    },\n    body: JSON.stringify({\n      id,\n      ...updates,\n      updated_by: updatedBy,\n      update_reason: updateReason\n    }),\n  })\n\n  if (!response.ok) throw new Error('Failed to update payment')\n  return response.json()\n}\n\nexport async function getFeePaymentByReceiptUrl(receiptUrl: string): Promise<FeePayment | null> {\n  const { data, error } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select(`\n      *,\n      student:IDCard(\n        *,\n        class:Class(name, section)\n      )\n    `)\n    .eq('receipt_url', receiptUrl)\n    .single()\n\n  if (error) {\n    if (error.code === 'PGRST116') return null // No rows returned\n    throw error\n  }\n  return data\n}\n\nexport async function getFeePaymentsByStudent(studentId: string): Promise<FeePayment[]> {\n  const { data, error } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select('*')\n    .eq('student_id', studentId)\n    .order('payment_date', { ascending: false })\n\n  if (error) throw error\n  return data || []\n}\n\nexport async function getAllFeePayments(): Promise<FeePayment[]> {\n  const { data, error } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select(`\n      *,\n      student:IDCard(*)\n    `)\n    .order('payment_date', { ascending: false })\n\n  if (error) throw error\n  return data || []\n}\n\nexport async function getStudentsWithPendingFees(): Promise<any[]> {\n  const currentMonth = new Date().getMonth() + 1\n  const currentYear = new Date().getFullYear()\n\n  // Get ALL students from IDCard table\n  const { data: allStudents, error: studentsError } = await supabase\n    .schema('school')\n    .from('IDCard')\n    .select(`\n      *,\n      class:Class(name, section)\n    `)\n\n  if (studentsError) throw studentsError\n\n  // Get ALL payments\n  const { data: allPayments, error: paymentsError } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select('*')\n\n  if (paymentsError) throw paymentsError\n\n  // Filter payments for current month with completed status and zero balance\n  const completedPaymentsThisMonth = (allPayments || []).filter(payment => {\n    const paymentDate = new Date(payment.payment_date)\n    const paymentMonth = paymentDate.getMonth() + 1\n    const paymentYear = paymentDate.getFullYear()\n\n    return paymentMonth === currentMonth &&\n           paymentYear === currentYear &&\n           payment.payment_status === 'completed' &&\n           payment.balance_remaining === 0\n  })\n\n  // Create set of students who have completed payments for current month\n  const studentsWithCompletedPayments = new Set(\n    completedPaymentsThisMonth.map(payment => payment.student_id)\n  )\n\n  // Group all payments by student_id\n  const paymentsByStudent = new Map()\n  allPayments?.forEach(payment => {\n    if (!paymentsByStudent.has(payment.student_id)) {\n      paymentsByStudent.set(payment.student_id, [])\n    }\n    paymentsByStudent.get(payment.student_id).push(payment)\n  })\n\n  // Return ALL students MINUS those with completed payments for current month\n  const studentsWithPending = (allStudents || [])\n    .filter(student => !studentsWithCompletedPayments.has(student.id))\n    .map(student => {\n      const studentPayments = paymentsByStudent.get(student.id) || []\n\n      // Calculate totals from all payments\n      const totalPaid = studentPayments.reduce((sum: number, payment: any) =>\n        sum + (parseFloat(payment.amount_received) || 0), 0)\n\n      const totalPending = studentPayments.reduce((sum: number, payment: any) =>\n        sum + (parseFloat(payment.balance_remaining) || 0), 0)\n\n      // Get last payment\n      const lastPayment = studentPayments.sort((a: any, b: any) =>\n        new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime()\n      )[0]\n\n      // Check if student has payment for current month using payment_date\n      const currentMonthPayment = studentPayments.find((payment: any) => {\n        const paymentDate = new Date(payment.payment_date)\n        return paymentDate.getMonth() + 1 === currentMonth &&\n               paymentDate.getFullYear() === currentYear\n      })\n\n      // Calculate pending amount\n      let pendingAmount = totalPending\n      if (!currentMonthPayment) {\n        pendingAmount += 1000 // Add default monthly fee if no payment for current month\n      }\n\n      return {\n        ...student,\n        totalPaid,\n        totalPending: pendingAmount,\n        lastPaymentDate: lastPayment?.payment_date,\n        lastPaymentAmount: lastPayment ? parseFloat(lastPayment.amount_received) : null,\n        pendingReason: !currentMonthPayment\n          ? `No payment for ${currentMonth}/${currentYear}`\n          : `Outstanding balance: ₹${totalPending}`\n      }\n    })\n\n  return studentsWithPending\n}\n\nexport async function getPaymentSummary(): Promise<{\n  totalCollected: number\n  totalPending: number\n  totalStudents: number\n  studentsWithPending: number\n}> {\n  const { data: payments, error: paymentsError } = await supabase\n    .schema('school')\n    .from('fee_payments')\n    .select('amount_received, balance_remaining')\n\n  if (paymentsError) throw paymentsError\n\n  const { data: students, error: studentsError } = await supabase\n    .schema('school')\n    .from('IDCard')\n    .select('id')\n\n  if (studentsError) throw studentsError\n\n  const totalCollected = payments?.reduce((sum, payment) => sum + payment.amount_received, 0) || 0\n  const totalPending = payments?.reduce((sum, payment) => sum + payment.balance_remaining, 0) || 0\n\n  // Count students with pending fees\n  const studentsWithPending = await getStudentsWithPendingFees()\n\n  return {\n    totalCollected,\n    totalPending,\n    totalStudents: students?.length || 0,\n    studentsWithPending: studentsWithPending.length\n  }\n}\n\n// Attendance operations\nexport async function markAttendance(attendanceData: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>): Promise<Attendance> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/attendance', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(attendanceData),\n    })\n    if (!response.ok) throw new Error('Failed to mark attendance')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('attendance')\n      .upsert(attendanceData, {\n        onConflict: 'student_id,attendance_date',\n        ignoreDuplicates: false\n      })\n      .select('*')\n      .single()\n\n    if (error) throw error\n    return data\n  }\n}\n\nexport async function bulkMarkAttendance(attendanceList: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>[]): Promise<Attendance[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/attendance/bulk', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({ attendanceList }),\n    })\n    if (!response.ok) throw new Error('Failed to mark bulk attendance')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('attendance')\n      .upsert(attendanceList, {\n        onConflict: 'student_id,attendance_date',\n        ignoreDuplicates: false\n      })\n      .select('*')\n\n    if (error) throw error\n    return data || []\n  }\n}\n\nexport async function getAttendanceByDate(date: string): Promise<Attendance[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch(`/api/attendance?date=${encodeURIComponent(date)}`)\n    if (!response.ok) throw new Error('Failed to fetch attendance')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('attendance')\n      .select(`\n        *,\n        student:IDCard(*)\n      `)\n      .eq('attendance_date', date)\n      .order('created_at', { ascending: false })\n\n    if (error) throw error\n    return data || []\n  }\n}\n\nexport async function getStudentsWithAttendanceForDate(date: string, classId?: string | null): Promise<(Student & { attendance?: Attendance })[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    let url = `/api/attendance/students?date=${encodeURIComponent(date)}`\n    if (classId) {\n      url += `&class=${encodeURIComponent(classId)}`\n    }\n    const response = await fetch(url)\n    if (!response.ok) throw new Error('Failed to fetch students with attendance')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    let studentsQuery = supabase\n      .schema('school')\n      .from('IDCard')\n      .select(`\n        *,\n        class:Class(name, section)\n      `)\n      .order('student_name')\n\n    if (classId && classId !== 'all') {\n      studentsQuery = studentsQuery.eq('class_id', classId)\n    }\n\n    const { data: students, error: studentsError } = await studentsQuery\n\n    if (studentsError) throw studentsError\n\n    const { data: attendance, error: attendanceError } = await supabase\n      .schema('school')\n      .from('attendance')\n      .select('*')\n      .eq('attendance_date', date)\n\n    if (attendanceError) throw attendanceError\n\n    // Merge students with their attendance data\n    const studentsWithAttendance = students?.map(student => {\n      const studentAttendance = attendance?.find(att => att.student_id === student.id)\n      return {\n        ...student,\n        attendance: studentAttendance\n      }\n    }) || []\n\n    return studentsWithAttendance\n  }\n}\n\nexport async function getAttendanceStatistics(date: string, classId?: string | null): Promise<{\n  totalStudents: number\n  presentCount: number\n  absentCount: number\n  attendancePercentage: number\n}> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    let url = `/api/attendance/statistics?date=${encodeURIComponent(date)}`\n    if (classId) {\n      url += `&class=${encodeURIComponent(classId)}`\n    }\n    const response = await fetch(url)\n    if (!response.ok) throw new Error('Failed to fetch attendance statistics')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    let studentsQuery = supabase\n      .schema('school')\n      .from('IDCard')\n      .select('id', { count: 'exact' })\n\n    if (classId && classId !== 'all') {\n      studentsQuery = studentsQuery.eq('class_id', classId)\n    }\n\n    const { data: totalStudents, error: studentsError } = await studentsQuery\n\n    if (studentsError) throw studentsError\n\n    let attendanceQuery = supabase\n      .schema('school')\n      .from('attendance')\n      .select(`\n        status,\n        student:IDCard!inner(class_id)\n      `)\n      .eq('attendance_date', date)\n\n    if (classId && classId !== 'all') {\n      attendanceQuery = attendanceQuery.eq('student.class_id', classId)\n    }\n\n    const { data: attendance, error: attendanceError } = await attendanceQuery\n\n    if (attendanceError) throw attendanceError\n\n    const total = totalStudents?.length || 0\n    const presentCount = attendance?.filter(att => att.status === 'present').length || 0\n    const absentCount = attendance?.filter(att => att.status === 'absent').length || 0\n    const attendancePercentage = total > 0 ? (presentCount / total) * 100 : 0\n\n    return {\n      totalStudents: total,\n      presentCount,\n      absentCount,\n      attendancePercentage: Math.round(attendancePercentage * 100) / 100\n    }\n  }\n}\n\nexport async function getAttendanceTrends(days: number = 30): Promise<{\n  date: string\n  presentCount: number\n  absentCount: number\n  attendancePercentage: number\n}[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch(`/api/attendance/trends?days=${days}`)\n    if (!response.ok) throw new Error('Failed to fetch attendance trends')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const endDate = new Date()\n    const startDate = new Date()\n    startDate.setDate(endDate.getDate() - days)\n\n    const { data: attendance, error } = await supabase\n      .schema('school')\n      .from('attendance')\n      .select('attendance_date, status')\n      .gte('attendance_date', startDate.toISOString().split('T')[0])\n      .lte('attendance_date', endDate.toISOString().split('T')[0])\n      .order('attendance_date')\n\n    if (error) throw error\n\n    const { data: totalStudents, error: studentsError } = await supabase\n      .schema('school')\n      .from('IDCard')\n      .select('id', { count: 'exact' })\n\n    if (studentsError) throw studentsError\n\n    const total = totalStudents?.length || 0\n\n    // Group by date and calculate statistics\n    const dateGroups = attendance?.reduce((acc, record) => {\n      const date = record.attendance_date\n      if (!acc[date]) {\n        acc[date] = { present: 0, absent: 0 }\n      }\n      if (record.status === 'present') {\n        acc[date].present++\n      } else {\n        acc[date].absent++\n      }\n      return acc\n    }, {} as Record<string, { present: number; absent: number }>) || {}\n\n    return Object.entries(dateGroups).map(([date, counts]) => ({\n      date,\n      presentCount: counts.present,\n      absentCount: counts.absent,\n      attendancePercentage: total > 0 ? Math.round((counts.present / total) * 10000) / 100 : 0\n    }))\n  }\n}\n\n// Attendance messaging operations\nexport async function saveAttendanceMessage(messageData: Omit<AttendanceMessage, 'id' | 'created_at' | 'sent_at'>): Promise<AttendanceMessage> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const response = await fetch('/api/attendance/messages', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(messageData),\n    })\n    if (!response.ok) throw new Error('Failed to save attendance message')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    const { data, error } = await supabase\n      .schema('school')\n      .from('attendance_messages')\n      .insert(messageData)\n      .select('*')\n      .single()\n\n    if (error) throw error\n    return data\n  }\n}\n\nexport async function getAttendanceMessages(date?: string): Promise<AttendanceMessage[]> {\n  if (typeof window !== 'undefined') {\n    // Client-side: use API route\n    const url = date ? `/api/attendance/messages?date=${encodeURIComponent(date)}` : '/api/attendance/messages'\n    const response = await fetch(url)\n    if (!response.ok) throw new Error('Failed to fetch attendance messages')\n    return response.json()\n  } else {\n    // Server-side: direct database access\n    let query = supabase\n      .schema('school')\n      .from('attendance_messages')\n      .select(`\n        *,\n        student:IDCard(*)\n      `)\n      .order('created_at', { ascending: false })\n\n    if (date) {\n      query = query.eq('attendance_date', date)\n    }\n\n    const { data, error } = await query\n\n    if (error) throw error\n    return data || []\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;AAIO,eAAe;IACpB,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,SACL,MAAM,CAAC,qBACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAGO,eAAe;IACpB,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,YACP,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QAEjB,yBAAyB;QACzB,MAAM,gBAAgB;eAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM,CAAC;SAAU;QACnF,OAAO;IACT;AACF;AAEO,eAAe,mBAAmB,SAAiB;IACxD,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,YAAY,WACf,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAEO,eAAe,eAAe,SAAiB;IACpD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,WACT,MAAM;IAET,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,KAAK,mBAAmB;;QAC9D,MAAM;IACR;IACA,OAAO;AACT;AAGO,eAAe,iBAAiB,OAA6F;IAClI,uCAAmC;;IAWnC,OAAO;QACL,sCAAsC;QACtC,MAAM,YAAY,OAAO,UAAU;QACnC,MAAM,aAAa,CAAC,SAAS,EAAE,WAAW;QAE1C,2CAA2C;QAC3C,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;QACjD,MAAM,YAAY,YAAY,QAAQ,KAAK;QAC3C,MAAM,WAAW,YAAY,WAAW;QAExC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC;YACN,GAAG,OAAO;YACV,aAAa;YACb;YACA;QACF,GACC,MAAM,CAAC,KACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAEO,eAAe,iBACpB,EAAU,EACV,OAA4B,EAC5B,YAAoB,QAAQ,EAC5B,YAAqB;IAErB,MAAM,WAAW,MAAM,MAAM,iBAAiB;QAC5C,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;QACA,MAAM,KAAK,SAAS,CAAC;YACnB;YACA,GAAG,OAAO;YACV,YAAY;YACZ,eAAe;QACjB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;IAClC,OAAO,SAAS,IAAI;AACtB;AAEO,eAAe,0BAA0B,UAAkB;IAChE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;;;;IAMT,CAAC,EACA,EAAE,CAAC,eAAe,YAClB,MAAM;IAET,IAAI,OAAO;QACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,KAAK,mBAAmB;;QAC9D,MAAM;IACR;IACA,OAAO;AACT;AAEO,eAAe,wBAAwB,SAAiB;IAC7D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,gBAAgB;QAAE,WAAW;IAAM;IAE5C,IAAI,OAAO,MAAM;IACjB,OAAO,QAAQ,EAAE;AACnB;AAEO,eAAe;IACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC,CAAC;;;IAGT,CAAC,EACA,KAAK,CAAC,gBAAgB;QAAE,WAAW;IAAM;IAE5C,IAAI,OAAO,MAAM;IACjB,OAAO,QAAQ,EAAE;AACnB;AAEO,eAAe;IACpB,MAAM,eAAe,IAAI,OAAO,QAAQ,KAAK;IAC7C,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qCAAqC;IACrC,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC/D,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;IAGT,CAAC;IAEH,IAAI,eAAe,MAAM;IAEzB,mBAAmB;IACnB,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC/D,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC;IAEV,IAAI,eAAe,MAAM;IAEzB,2EAA2E;IAC3E,MAAM,6BAA6B,CAAC,eAAe,EAAE,EAAE,MAAM,CAAC,CAAA;QAC5D,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;QACjD,MAAM,eAAe,YAAY,QAAQ,KAAK;QAC9C,MAAM,cAAc,YAAY,WAAW;QAE3C,OAAO,iBAAiB,gBACjB,gBAAgB,eAChB,QAAQ,cAAc,KAAK,eAC3B,QAAQ,iBAAiB,KAAK;IACvC;IAEA,uEAAuE;IACvE,MAAM,gCAAgC,IAAI,IACxC,2BAA2B,GAAG,CAAC,CAAA,UAAW,QAAQ,UAAU;IAG9D,mCAAmC;IACnC,MAAM,oBAAoB,IAAI;IAC9B,aAAa,QAAQ,CAAA;QACnB,IAAI,CAAC,kBAAkB,GAAG,CAAC,QAAQ,UAAU,GAAG;YAC9C,kBAAkB,GAAG,CAAC,QAAQ,UAAU,EAAE,EAAE;QAC9C;QACA,kBAAkB,GAAG,CAAC,QAAQ,UAAU,EAAE,IAAI,CAAC;IACjD;IAEA,4EAA4E;IAC5E,MAAM,sBAAsB,CAAC,eAAe,EAAE,EAC3C,MAAM,CAAC,CAAA,UAAW,CAAC,8BAA8B,GAAG,CAAC,QAAQ,EAAE,GAC/D,GAAG,CAAC,CAAA;QACH,MAAM,kBAAkB,kBAAkB,GAAG,CAAC,QAAQ,EAAE,KAAK,EAAE;QAE/D,qCAAqC;QACrC,MAAM,YAAY,gBAAgB,MAAM,CAAC,CAAC,KAAa,UACrD,MAAM,CAAC,WAAW,QAAQ,eAAe,KAAK,CAAC,GAAG;QAEpD,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAC,KAAa,UACxD,MAAM,CAAC,WAAW,QAAQ,iBAAiB,KAAK,CAAC,GAAG;QAEtD,mBAAmB;QACnB,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAC,GAAQ,IAChD,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,YAAY,EAAE,OAAO,GACtE,CAAC,EAAE;QAEJ,oEAAoE;QACpE,MAAM,sBAAsB,gBAAgB,IAAI,CAAC,CAAC;YAChD,MAAM,cAAc,IAAI,KAAK,QAAQ,YAAY;YACjD,OAAO,YAAY,QAAQ,KAAK,MAAM,gBAC/B,YAAY,WAAW,OAAO;QACvC;QAEA,2BAA2B;QAC3B,IAAI,gBAAgB;QACpB,IAAI,CAAC,qBAAqB;YACxB,iBAAiB,KAAK,0DAA0D;;QAClF;QAEA,OAAO;YACL,GAAG,OAAO;YACV;YACA,cAAc;YACd,iBAAiB,aAAa;YAC9B,mBAAmB,cAAc,WAAW,YAAY,eAAe,IAAI;YAC3E,eAAe,CAAC,sBACZ,CAAC,eAAe,EAAE,aAAa,CAAC,EAAE,aAAa,GAC/C,CAAC,sBAAsB,EAAE,cAAc;QAC7C;IACF;IAEF,OAAO;AACT;AAEO,eAAe;IAMpB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5D,MAAM,CAAC,UACP,IAAI,CAAC,gBACL,MAAM,CAAC;IAEV,IAAI,eAAe,MAAM;IAEzB,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC5D,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC;IAEV,IAAI,eAAe,MAAM;IAEzB,MAAM,iBAAiB,UAAU,OAAO,CAAC,KAAK,UAAY,MAAM,QAAQ,eAAe,EAAE,MAAM;IAC/F,MAAM,eAAe,UAAU,OAAO,CAAC,KAAK,UAAY,MAAM,QAAQ,iBAAiB,EAAE,MAAM;IAE/F,mCAAmC;IACnC,MAAM,sBAAsB,MAAM;IAElC,OAAO;QACL;QACA;QACA,eAAe,UAAU,UAAU;QACnC,qBAAqB,oBAAoB,MAAM;IACjD;AACF;AAGO,eAAe,eAAe,cAAoE;IACvG,uCAAmC;;IAWnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,gBAAgB;YACtB,YAAY;YACZ,kBAAkB;QACpB,GACC,MAAM,CAAC,KACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAEO,eAAe,mBAAmB,cAAsE;IAC7G,uCAAmC;;IAWnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,gBAAgB;YACtB,YAAY;YACZ,kBAAkB;QACpB,GACC,MAAM,CAAC;QAEV,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAEO,eAAe,oBAAoB,IAAY;IACpD,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,mBAAmB,MACtB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF;AAEO,eAAe,iCAAiC,IAAY,EAAE,OAAuB;IAC1F,uCAAmC;;IASnC,OAAO;QACL,sCAAsC;QACtC,IAAI,gBAAgB,sHAAA,CAAA,WAAQ,CACzB,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC;QAET,IAAI,WAAW,YAAY,OAAO;YAChC,gBAAgB,cAAc,EAAE,CAAC,YAAY;QAC/C;QAEA,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM;QAEvD,IAAI,eAAe,MAAM;QAEzB,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAChE,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,KACP,EAAE,CAAC,mBAAmB;QAEzB,IAAI,iBAAiB,MAAM;QAE3B,4CAA4C;QAC5C,MAAM,yBAAyB,UAAU,IAAI,CAAA;YAC3C,MAAM,oBAAoB,YAAY,KAAK,CAAA,MAAO,IAAI,UAAU,KAAK,QAAQ,EAAE;YAC/E,OAAO;gBACL,GAAG,OAAO;gBACV,YAAY;YACd;QACF,MAAM,EAAE;QAER,OAAO;IACT;AACF;AAEO,eAAe,wBAAwB,IAAY,EAAE,OAAuB;IAMjF,uCAAmC;;IASnC,OAAO;QACL,sCAAsC;QACtC,IAAI,gBAAgB,sHAAA,CAAA,WAAQ,CACzB,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,MAAM;YAAE,OAAO;QAAQ;QAEjC,IAAI,WAAW,YAAY,OAAO;YAChC,gBAAgB,cAAc,EAAE,CAAC,YAAY;QAC/C;QAEA,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM;QAE5D,IAAI,eAAe,MAAM;QAEzB,IAAI,kBAAkB,sHAAA,CAAA,WAAQ,CAC3B,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,EAAE,CAAC,mBAAmB;QAEzB,IAAI,WAAW,YAAY,OAAO;YAChC,kBAAkB,gBAAgB,EAAE,CAAC,oBAAoB;QAC3D;QAEA,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM;QAE3D,IAAI,iBAAiB,MAAM;QAE3B,MAAM,QAAQ,eAAe,UAAU;QACvC,MAAM,eAAe,YAAY,OAAO,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,UAAU;QACnF,MAAM,cAAc,YAAY,OAAO,CAAA,MAAO,IAAI,MAAM,KAAK,UAAU,UAAU;QACjF,MAAM,uBAAuB,QAAQ,IAAI,AAAC,eAAe,QAAS,MAAM;QAExE,OAAO;YACL,eAAe;YACf;YACA;YACA,sBAAsB,KAAK,KAAK,CAAC,uBAAuB,OAAO;QACjE;IACF;AACF;AAEO,eAAe,oBAAoB,OAAe,EAAE;IAMzD,uCAAmC;;IAKnC,OAAO;QACL,sCAAsC;QACtC,MAAM,UAAU,IAAI;QACpB,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,QAAQ,OAAO,KAAK;QAEtC,MAAM,EAAE,MAAM,UAAU,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC/C,MAAM,CAAC,UACP,IAAI,CAAC,cACL,MAAM,CAAC,2BACP,GAAG,CAAC,mBAAmB,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAC5D,GAAG,CAAC,mBAAmB,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAC1D,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QAEjB,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACjE,MAAM,CAAC,UACP,IAAI,CAAC,UACL,MAAM,CAAC,MAAM;YAAE,OAAO;QAAQ;QAEjC,IAAI,eAAe,MAAM;QAEzB,MAAM,QAAQ,eAAe,UAAU;QAEvC,yCAAyC;QACzC,MAAM,aAAa,YAAY,OAAO,CAAC,KAAK;YAC1C,MAAM,OAAO,OAAO,eAAe;YACnC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE;gBACd,GAAG,CAAC,KAAK,GAAG;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;YACtC;YACA,IAAI,OAAO,MAAM,KAAK,WAAW;gBAC/B,GAAG,CAAC,KAAK,CAAC,OAAO;YACnB,OAAO;gBACL,GAAG,CAAC,KAAK,CAAC,MAAM;YAClB;YACA,OAAO;QACT,GAAG,CAAC,MAA6D,CAAC;QAElE,OAAO,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,MAAM,OAAO,GAAK,CAAC;gBACzD;gBACA,cAAc,OAAO,OAAO;gBAC5B,aAAa,OAAO,MAAM;gBAC1B,sBAAsB,QAAQ,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,OAAO,GAAG,QAAS,SAAS,MAAM;YACzF,CAAC;IACH;AACF;AAGO,eAAe,sBAAsB,WAAqE;IAC/G,uCAAmC;;IAWnC,OAAO;QACL,sCAAsC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,MAAM,CAAC,UACP,IAAI,CAAC,uBACL,MAAM,CAAC,aACP,MAAM,CAAC,KACP,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;AACF;AAEO,eAAe,sBAAsB,IAAa;IACvD,uCAAmC;;IAMnC,OAAO;QACL,sCAAsC;QACtC,IAAI,QAAQ,sHAAA,CAAA,WAAQ,CACjB,MAAM,CAAC,UACP,IAAI,CAAC,uBACL,MAAM,CAAC,CAAC;;;MAGT,CAAC,EACA,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,MAAM;YACR,QAAQ,MAAM,EAAE,CAAC,mBAAmB;QACtC;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;AACF", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { format } from 'date-fns'\nimport { Student } from '@/types/database'\nimport { getClassesWithNames, getStudentsByClass, createFeePayment } from '@/lib/database'\nimport { CreditCard, Calendar, DollarSign, FileText, Share2 } from 'lucide-react'\n\nconst feePaymentSchema = z.object({\n  student_id: z.string().min(1, 'Please select a student'),\n  amount_received: z.number().min(0.01, 'Amount must be greater than 0'),\n  payment_date: z.string().min(1, 'Payment date is required'),\n  payment_method: z.enum(['cash', 'card', 'upi', 'bank_transfer', 'cheque']),\n  balance_remaining: z.number().min(0, 'Balance cannot be negative'),\n  payment_status: z.enum(['completed', 'partial', 'pending']),\n  notes: z.string().optional(),\n})\n\ntype FeePaymentForm = z.infer<typeof feePaymentSchema>\n\nexport default function FeeManagementForm() {\n  const [classes, setClasses] = useState<{id: string, name: string, section: string}[]>([])\n  const [students, setStudents] = useState<Student[]>([])\n  const [selectedClass, setSelectedClass] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [receiptUrl, setReceiptUrl] = useState('')\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    reset,\n    formState: { errors }\n  } = useForm<FeePaymentForm>({\n    resolver: zodResolver(feePaymentSchema),\n    defaultValues: {\n      payment_date: format(new Date(), 'yyyy-MM-dd'),\n      payment_method: 'cash',\n      payment_status: 'completed',\n      balance_remaining: 0,\n    }\n  })\n\n  const selectedStudentId = watch('student_id')\n  const selectedStudent = students.find(s => s.id === selectedStudentId)\n  const selectedClassInfo = classes.find(c => c.id === selectedClass)\n\n  useEffect(() => {\n    loadClasses()\n  }, [])\n\n  useEffect(() => {\n    if (selectedClass) {\n      loadStudents(selectedClass)\n    }\n  }, [selectedClass])\n\n  const loadClasses = async () => {\n    try {\n      const classData = await getClassesWithNames()\n      setClasses(classData)\n    } catch (error) {\n      console.error('Error loading classes:', error)\n    }\n  }\n\n  const loadStudents = async (className: string) => {\n    try {\n      const studentData = await getStudentsByClass(className)\n      setStudents(studentData)\n    } catch (error) {\n      console.error('Error loading students:', error)\n    }\n  }\n\n  const onSubmit = async (data: FeePaymentForm) => {\n    setLoading(true)\n    try {\n      const payment = await createFeePayment(data)\n      setReceiptUrl(`${window.location.origin}${payment.receipt_url}`)\n      reset()\n      setSelectedClass('')\n      setStudents([])\n    } catch (error) {\n      console.error('Error creating payment:', error)\n      alert('Error creating payment. Please try again.')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const shareOnWhatsApp = () => {\n    if (receiptUrl && selectedStudent) {\n      const message = `Fee Receipt - ${selectedStudent.student_name}\\n\\nDear Parent,\\n\\nYour fee payment has been recorded. Please view and download your receipt:\\n\\n${receiptUrl}\\n\\nThank you!\\n${process.env.NEXT_PUBLIC_SCHOOL_NAME || 'First Step School'}`\n      const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`\n      window.open(whatsappUrl, '_blank')\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n        {/* Class Selection */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Select Class\n          </label>\n          <select\n            value={selectedClass}\n            onChange={(e) => {\n              setSelectedClass(e.target.value)\n              setValue('student_id', '')\n            }}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900\"\n          >\n            <option value=\"\" className=\"text-gray-500\">Choose a class...</option>\n            {classes.map((classItem) => (\n              <option key={classItem.id} value={classItem.id} className=\"text-gray-900\">\n                {classItem.name} - {classItem.section}\n              </option>\n            ))}\n          </select>\n        </div>\n\n        {/* Student Selection */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Select Student\n          </label>\n          <select\n            {...register('student_id')}\n            disabled={!selectedClass}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 bg-white text-gray-900\"\n          >\n            <option value=\"\" className=\"text-gray-500\">Choose a student...</option>\n            {students.map((student) => (\n              <option key={student.id} value={student.id} className=\"text-gray-900\">\n                {student.student_name} - {student.father_name}\n              </option>\n            ))}\n          </select>\n          {errors.student_id && (\n            <p className=\"mt-1 text-sm text-red-600\">{errors.student_id.message}</p>\n          )}\n        </div>\n\n        {/* Student Details Display */}\n        {selectedStudent && (\n          <div className=\"bg-blue-50 p-4 rounded-lg\">\n            <h3 className=\"font-medium text-blue-900 mb-2\">Student Details</h3>\n            <div className=\"grid grid-cols-2 gap-4 text-sm\">\n              <div>\n                <span className=\"font-medium\">Student:</span> {selectedStudent.student_name}\n              </div>\n              <div>\n                <span className=\"font-medium\">Class:</span> {selectedClassInfo ? `${selectedClassInfo.name} - ${selectedClassInfo.section}` : selectedStudent.class_id}\n              </div>\n              <div>\n                <span className=\"font-medium\">Father:</span> {selectedStudent.father_name}\n              </div>\n              <div>\n                <span className=\"font-medium\">Mother:</span> {selectedStudent.mother_name}\n              </div>\n              <div>\n                <span className=\"font-medium\">Father Mobile:</span> {selectedStudent.father_mobile || 'N/A'}\n              </div>\n              <div>\n                <span className=\"font-medium\">Mother Mobile:</span> {selectedStudent.mother_mobile || 'N/A'}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Payment Details */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <DollarSign className=\"inline w-4 h-4 mr-1\" />\n              Amount Received\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              {...register('amount_received', { valueAsNumber: true })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500\"\n              placeholder=\"0.00\"\n            />\n            {errors.amount_received && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.amount_received.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <Calendar className=\"inline w-4 h-4 mr-1\" />\n              Payment Date\n            </label>\n            <input\n              type=\"date\"\n              {...register('payment_date')}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900\"\n            />\n            {errors.payment_date && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.payment_date.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              <CreditCard className=\"inline w-4 h-4 mr-1\" />\n              Payment Method\n            </label>\n            <select\n              {...register('payment_method')}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900\"\n            >\n              <option value=\"cash\" className=\"text-gray-900\">Cash</option>\n              <option value=\"card\" className=\"text-gray-900\">Card</option>\n              <option value=\"upi\" className=\"text-gray-900\">UPI</option>\n              <option value=\"bank_transfer\" className=\"text-gray-900\">Bank Transfer</option>\n              <option value=\"cheque\" className=\"text-gray-900\">Cheque</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Balance Remaining\n            </label>\n            <input\n              type=\"number\"\n              step=\"0.01\"\n              {...register('balance_remaining', { valueAsNumber: true })}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500\"\n              placeholder=\"0.00\"\n            />\n            {errors.balance_remaining && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.balance_remaining.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Payment Status\n            </label>\n            <select\n              {...register('payment_status')}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900\"\n            >\n              <option value=\"completed\" className=\"text-gray-900\">Completed</option>\n              <option value=\"partial\" className=\"text-gray-900\">Partial</option>\n              <option value=\"pending\" className=\"text-gray-900\">Pending</option>\n            </select>\n          </div>\n        </div>\n\n        {/* Notes */}\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            <FileText className=\"inline w-4 h-4 mr-1\" />\n            Additional Notes\n          </label>\n          <textarea\n            {...register('notes')}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500\"\n            placeholder=\"Any additional notes about the payment...\"\n          />\n        </div>\n\n        {/* Submit Button */}\n        <button\n          type=\"submit\"\n          disabled={loading}\n          className=\"w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {loading ? 'Processing...' : 'Record Payment & Generate Receipt'}\n        </button>\n      </form>\n\n      {/* Receipt Generated */}\n      {receiptUrl && (\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-6\">\n          <h3 className=\"text-lg font-medium text-green-900 mb-4\">\n            Receipt Generated Successfully!\n          </h3>\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-green-700 mb-2\">\n                Receipt URL:\n              </label>\n              <div className=\"flex gap-2\">\n                <input\n                  type=\"text\"\n                  value={receiptUrl}\n                  readOnly\n                  className=\"flex-1 px-3 py-2 border border-green-300 rounded-md bg-white\"\n                />\n                <button\n                  onClick={() => navigator.clipboard.writeText(receiptUrl)}\n                  className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\"\n                >\n                  Copy\n                </button>\n              </div>\n            </div>\n            <div className=\"flex gap-4\">\n              <a\n                href={receiptUrl}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700\"\n              >\n                <FileText className=\"w-4 h-4\" />\n                View Receipt\n              </a>\n              <button\n                onClick={shareOnWhatsApp}\n                className=\"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\"\n              >\n                <Share2 className=\"w-4 h-4\" />\n                Share on WhatsApp\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;AAWA,MAAM,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,YAAY,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,iBAAiB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM;IACtC,cAAc,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAQ;QAAO;QAAiB;KAAS;IACzE,mBAAmB,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,gBAAgB,iLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAW;KAAU;IAC1D,OAAO,iLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAIe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiD,EAAE;IACxF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAkB;QAC1B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,cAAc,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;YACjC,gBAAgB;YAChB,gBAAgB;YAChB,mBAAmB;QACrB;IACF;IAEA,MAAM,oBAAoB,MAAM;IAChC,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACpD,MAAM,oBAAoB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,aAAa;QACf;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,YAAY,MAAM,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD;YAC1C,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;YAC7C,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,IAAI;YACF,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACvC,cAAc,GAAG,OAAO,QAAQ,CAAC,MAAM,GAAG,QAAQ,WAAW,EAAE;YAC/D;YACA,iBAAiB;YACjB,YAAY,EAAE;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,cAAc,iBAAiB;YACjC,MAAM,UAAU,CAAC,cAAc,EAAE,gBAAgB,YAAY,CAAC,kGAAkG,EAAE,WAAW,gBAAgB,EAAE,yDAAuC,qBAAqB;YAC3P,MAAM,cAAc,CAAC,oBAAoB,EAAE,mBAAmB,UAAU;YACxE,OAAO,IAAI,CAAC,aAAa;QAC3B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;kCAEhD,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,OAAO;gCACP,UAAU,CAAC;oCACT,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAC/B,SAAS,cAAc;gCACzB;gCACA,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;wCAAG,WAAU;kDAAgB;;;;;;oCAC1C,QAAQ,GAAG,CAAC,CAAC,0BACZ,8OAAC;4CAA0B,OAAO,UAAU,EAAE;4CAAE,WAAU;;gDACvD,UAAU,IAAI;gDAAC;gDAAI,UAAU,OAAO;;2CAD1B,UAAU,EAAE;;;;;;;;;;;;;;;;;kCAQ/B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACE,GAAG,SAAS,aAAa;gCAC1B,UAAU,CAAC;gCACX,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;wCAAG,WAAU;kDAAgB;;;;;;oCAC1C,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4CAAwB,OAAO,QAAQ,EAAE;4CAAE,WAAU;;gDACnD,QAAQ,YAAY;gDAAC;gDAAI,QAAQ,WAAW;;2CADlC,QAAQ,EAAE;;;;;;;;;;;4BAK1B,OAAO,UAAU,kBAChB,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;oBAKtE,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAe;4CAAE,gBAAgB,YAAY;;;;;;;kDAE7E,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAa;4CAAE,oBAAoB,GAAG,kBAAkB,IAAI,CAAC,GAAG,EAAE,kBAAkB,OAAO,EAAE,GAAG,gBAAgB,QAAQ;;;;;;;kDAExJ,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAc;4CAAE,gBAAgB,WAAW;;;;;;;kDAE3E,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAc;4CAAE,gBAAgB,WAAW;;;;;;;kDAE3E,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAqB;4CAAE,gBAAgB,aAAa,IAAI;;;;;;;kDAExF,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;0DAAc;;;;;;4CAAqB;4CAAE,gBAAgB,aAAa,IAAI;;;;;;;;;;;;;;;;;;;kCAO9F,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGhD,8OAAC;wCACC,MAAK;wCACL,MAAK;wCACJ,GAAG,SAAS,mBAAmB;4CAAE,eAAe;wCAAK,EAAE;wCACxD,WAAU;wCACV,aAAY;;;;;;oCAEb,OAAO,eAAe,kBACrB,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,eAAe,CAAC,OAAO;;;;;;;;;;;;0CAI5E,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG9C,8OAAC;wCACC,MAAK;wCACJ,GAAG,SAAS,eAAe;wCAC5B,WAAU;;;;;;oCAEX,OAAO,YAAY,kBAClB,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;0CAIzE,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;0DACf,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAGhD,8OAAC;wCACE,GAAG,SAAS,iBAAiB;wCAC9B,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;gDAAO,WAAU;0DAAgB;;;;;;0DAC/C,8OAAC;gDAAO,OAAM;gDAAO,WAAU;0DAAgB;;;;;;0DAC/C,8OAAC;gDAAO,OAAM;gDAAM,WAAU;0DAAgB;;;;;;0DAC9C,8OAAC;gDAAO,OAAM;gDAAgB,WAAU;0DAAgB;;;;;;0DACxD,8OAAC;gDAAO,OAAM;gDAAS,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAIrD,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,MAAK;wCACJ,GAAG,SAAS,qBAAqB;4CAAE,eAAe;wCAAK,EAAE;wCAC1D,WAAU;wCACV,aAAY;;;;;;oCAEb,OAAO,iBAAiB,kBACvB,8OAAC;wCAAE,WAAU;kDAA6B,OAAO,iBAAiB,CAAC,OAAO;;;;;;;;;;;;0CAI9E,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACE,GAAG,SAAS,iBAAiB;wCAC9B,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;gDAAY,WAAU;0DAAgB;;;;;;0DACpD,8OAAC;gDAAO,OAAM;gDAAU,WAAU;0DAAgB;;;;;;0DAClD,8OAAC;gDAAO,OAAM;gDAAU,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAMxD,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;kDACf,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAwB;;;;;;;0CAG9C,8OAAC;gCACE,GAAG,SAAS,QAAQ;gCACrB,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAKhB,8OAAC;wBACC,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,kBAAkB;;;;;;;;;;;;YAKhC,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;kCAGxD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAgD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC;gDACC,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC;gDAC7C,WAAU;0DACX;;;;;;;;;;;;;;;;;;0CAKL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAM;wCACN,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/components/FeeRecordsComponent.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { format } from 'date-fns'\nimport { Search, Filter, Download, Eye, ChevronLeft, ChevronRight, Edit, Save, X, History } from 'lucide-react'\nimport { FeePayment, FeeHistoryUpdate } from '@/types/database'\nimport { updateFeePayment } from '@/lib/database'\n\ninterface FeeRecordsFilters {\n  studentName: string\n  className: string\n  status: string\n  method: string\n  startDate: string\n  endDate: string\n}\n\nexport default function FeeRecordsComponent() {\n  const [payments, setPayments] = useState<FeePayment[]>([])\n  const [loading, setLoading] = useState(true)\n  const [currentPage, setCurrentPage] = useState(1)\n  const [totalPages, setTotalPages] = useState(1)\n  const [showFilters, setShowFilters] = useState(false)\n  const [editingPayment, setEditingPayment] = useState<string | null>(null)\n  const [editForm, setEditForm] = useState<Partial<FeePayment>>({})\n  const [showHistory, setShowHistory] = useState<string | null>(null)\n  const [historyData, setHistoryData] = useState<FeeHistoryUpdate[]>([])\n  const [updateReason, setUpdateReason] = useState('')\n  const [filters, setFilters] = useState<FeeRecordsFilters>({\n    studentName: '',\n    className: '',\n    status: '',\n    method: '',\n    startDate: '',\n    endDate: ''\n  })\n\n  const fetchPayments = async () => {\n    setLoading(true)\n    try {\n      const params = new URLSearchParams({\n        page: currentPage.toString(),\n        limit: '10',\n        sortBy: 'payment_date',\n        sortOrder: 'desc',\n        ...(filters.status && { status: filters.status }),\n        ...(filters.method && { method: filters.method }),\n        ...(filters.studentName && { studentName: filters.studentName }),\n        ...(filters.className && { className: filters.className }),\n        ...(filters.startDate && { startDate: filters.startDate }),\n        ...(filters.endDate && { endDate: filters.endDate })\n      })\n\n      const response = await fetch(`/api/payments?${params}`)\n      if (!response.ok) throw new Error('Failed to fetch payments')\n      \n      const result = await response.json()\n      setPayments(result.data)\n      setTotalPages(result.pagination.totalPages)\n    } catch (error) {\n      console.error('Error fetching payments:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchPayments()\n  }, [currentPage, filters])\n\n  const handleFilterChange = (key: keyof FeeRecordsFilters, value: string) => {\n    setFilters(prev => ({ ...prev, [key]: value }))\n    setCurrentPage(1) // Reset to first page when filtering\n  }\n\n  const clearFilters = () => {\n    setFilters({\n      studentName: '',\n      className: '',\n      status: '',\n      method: '',\n      startDate: '',\n      endDate: ''\n    })\n    setCurrentPage(1)\n  }\n\n  const exportToCSV = () => {\n    const headers = ['Date', 'Student Name', 'Class', 'Amount', 'Method', 'Status', 'Balance', 'Notes']\n    const csvData = payments.map(payment => [\n      format(new Date(payment.payment_date), 'dd/MM/yyyy'),\n      payment.student?.student_name || '',\n      payment.student?.class ? `${payment.student.class.name} ${payment.student.class.section}` : '',\n      payment.amount_received,\n      payment.payment_method,\n      payment.payment_status,\n      payment.balance_remaining,\n      payment.notes || ''\n    ])\n\n    const csvContent = [headers, ...csvData]\n      .map(row => row.map(field => `\"${field}\"`).join(','))\n      .join('\\n')\n\n    const blob = new Blob([csvContent], { type: 'text/csv' })\n    const url = window.URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `fee-records-${format(new Date(), 'yyyy-MM-dd')}.csv`\n    a.click()\n    window.URL.revokeObjectURL(url)\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'completed':\n        return 'bg-green-100 text-green-800'\n      case 'partial':\n        return 'bg-yellow-100 text-yellow-800'\n      case 'pending':\n        return 'bg-red-100 text-red-800'\n      default:\n        return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatPaymentMethod = (method: string) => {\n    switch (method) {\n      case 'bank_transfer':\n        return 'Bank Transfer'\n      case 'upi':\n        return 'UPI'\n      default:\n        return method.charAt(0).toUpperCase() + method.slice(1)\n    }\n  }\n\n  const handleEditClick = (payment: FeePayment) => {\n    setEditingPayment(payment.id)\n    setEditForm({\n      amount_received: payment.amount_received,\n      payment_date: payment.payment_date,\n      payment_method: payment.payment_method,\n      payment_status: payment.payment_status,\n      balance_remaining: payment.balance_remaining,\n      notes: payment.notes\n    })\n    setUpdateReason('')\n  }\n\n  const handleSaveEdit = async () => {\n    if (!editingPayment || !updateReason.trim()) {\n      alert('Please provide a reason for the update')\n      return\n    }\n\n    try {\n      await updateFeePayment(editingPayment, editForm, 'admin', updateReason)\n      setEditingPayment(null)\n      setEditForm({})\n      setUpdateReason('')\n      fetchPayments() // Refresh the data\n    } catch (error) {\n      console.error('Error updating payment:', error)\n      alert('Failed to update payment record')\n    }\n  }\n\n  const handleCancelEdit = () => {\n    setEditingPayment(null)\n    setEditForm({})\n    setUpdateReason('')\n  }\n\n  const fetchHistory = async (paymentId: string) => {\n    try {\n      const response = await fetch(`/api/fee-history?fee_payment_id=${paymentId}`)\n      if (!response.ok) throw new Error('Failed to fetch history')\n      const history = await response.json()\n      setHistoryData(history)\n      setShowHistory(paymentId)\n    } catch (error) {\n      console.error('Error fetching history:', error)\n      alert('Failed to fetch payment history')\n    }\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Actions */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n        <div>\n          <h3 className=\"text-lg font-medium text-gray-900\">Fee Payment Records</h3>\n          <p className=\"text-sm text-gray-500\">View and manage all fee payment submissions</p>\n        </div>\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => setShowFilters(!showFilters)}\n            className=\"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700\"\n          >\n            <Filter className=\"w-4 h-4\" />\n            Filters\n          </button>\n          <button\n            onClick={exportToCSV}\n            className=\"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700\"\n          >\n            <Download className=\"w-4 h-4\" />\n            Export CSV\n          </button>\n        </div>\n      </div>\n\n      {/* Filters Panel */}\n      {showFilters && (\n        <div className=\"bg-gray-50 p-4 rounded-lg border\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Student Name</label>\n              <input\n                type=\"text\"\n                value={filters.studentName}\n                onChange={(e) => handleFilterChange('studentName', e.target.value)}\n                placeholder=\"Search by student name...\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\n              <input\n                type=\"text\"\n                value={filters.className}\n                onChange={(e) => handleFilterChange('className', e.target.value)}\n                placeholder=\"Search by class...\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Payment Status</label>\n              <select\n                value={filters.status}\n                onChange={(e) => handleFilterChange('status', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n              >\n                <option value=\"\">All Statuses</option>\n                <option value=\"completed\">Completed</option>\n                <option value=\"partial\">Partial</option>\n                <option value=\"pending\">Pending</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Payment Method</label>\n              <select\n                value={filters.method}\n                onChange={(e) => handleFilterChange('method', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n              >\n                <option value=\"\">All Methods</option>\n                <option value=\"cash\">Cash</option>\n                <option value=\"card\">Card</option>\n                <option value=\"upi\">UPI</option>\n                <option value=\"bank_transfer\">Bank Transfer</option>\n                <option value=\"cheque\">Cheque</option>\n              </select>\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">Start Date</label>\n              <input\n                type=\"date\"\n                value={filters.startDate}\n                onChange={(e) => handleFilterChange('startDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">End Date</label>\n              <input\n                type=\"date\"\n                value={filters.endDate}\n                onChange={(e) => handleFilterChange('endDate', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              />\n            </div>\n          </div>\n          <div className=\"mt-4 flex justify-end\">\n            <button\n              onClick={clearFilters}\n              className=\"px-4 py-2 text-sm text-gray-600 hover:text-gray-800\"\n            >\n              Clear All Filters\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Records Table */}\n      <div className=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\n        {loading ? (\n          <div className=\"p-8 text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-500\">Loading fee records...</p>\n          </div>\n        ) : payments.length === 0 ? (\n          <div className=\"p-8 text-center\">\n            <p className=\"text-gray-500\">No fee records found matching your criteria.</p>\n          </div>\n        ) : (\n          <>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Date\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Student\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Class\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Amount\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Method\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Status\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Balance\n                    </th>\n                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                      Actions\n                    </th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {payments.map((payment) => (\n                    <tr key={payment.id} className=\"hover:bg-gray-50\">\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {editingPayment === payment.id ? (\n                          <input\n                            type=\"date\"\n                            value={editForm.payment_date || ''}\n                            onChange={(e) => setEditForm(prev => ({ ...prev, payment_date: e.target.value }))}\n                            className=\"w-full px-2 py-1 border border-gray-300 rounded text-gray-900\"\n                          />\n                        ) : (\n                          format(new Date(payment.payment_date), 'dd/MM/yyyy')\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {payment.student?.student_name || 'N/A'}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {payment.student?.class ? `${payment.student.class.name} ${payment.student.class.section}` : 'N/A'}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {editingPayment === payment.id ? (\n                          <input\n                            type=\"number\"\n                            step=\"0.01\"\n                            value={editForm.amount_received || ''}\n                            onChange={(e) => setEditForm(prev => ({ ...prev, amount_received: parseFloat(e.target.value) }))}\n                            className=\"w-full px-2 py-1 border border-gray-300 rounded text-gray-900\"\n                          />\n                        ) : (\n                          `₹${payment.amount_received.toLocaleString()}`\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {editingPayment === payment.id ? (\n                          <select\n                            value={editForm.payment_method || ''}\n                            onChange={(e) => setEditForm(prev => ({ ...prev, payment_method: e.target.value as any }))}\n                            className=\"w-full px-2 py-1 border border-gray-300 rounded text-gray-900\"\n                          >\n                            <option value=\"cash\">Cash</option>\n                            <option value=\"card\">Card</option>\n                            <option value=\"upi\">UPI</option>\n                            <option value=\"bank_transfer\">Bank Transfer</option>\n                            <option value=\"cheque\">Cheque</option>\n                          </select>\n                        ) : (\n                          formatPaymentMethod(payment.payment_method)\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\n                        {editingPayment === payment.id ? (\n                          <select\n                            value={editForm.payment_status || ''}\n                            onChange={(e) => setEditForm(prev => ({ ...prev, payment_status: e.target.value as any }))}\n                            className=\"w-full px-2 py-1 border border-gray-300 rounded text-gray-900\"\n                          >\n                            <option value=\"completed\">Completed</option>\n                            <option value=\"partial\">Partial</option>\n                            <option value=\"pending\">Pending</option>\n                          </select>\n                        ) : (\n                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.payment_status)}`}>\n                            {payment.payment_status}\n                          </span>\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                        {editingPayment === payment.id ? (\n                          <input\n                            type=\"number\"\n                            step=\"0.01\"\n                            value={editForm.balance_remaining || ''}\n                            onChange={(e) => setEditForm(prev => ({ ...prev, balance_remaining: parseFloat(e.target.value) }))}\n                            className=\"w-full px-2 py-1 border border-gray-300 rounded text-gray-900\"\n                          />\n                        ) : (\n                          `₹${payment.balance_remaining.toLocaleString()}`\n                        )}\n                      </td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        <div className=\"flex items-center gap-2\">\n                          {editingPayment === payment.id ? (\n                            <>\n                              <button\n                                onClick={handleSaveEdit}\n                                className=\"text-green-600 hover:text-green-900\"\n                                title=\"Save changes\"\n                              >\n                                <Save className=\"w-4 h-4\" />\n                              </button>\n                              <button\n                                onClick={handleCancelEdit}\n                                className=\"text-red-600 hover:text-red-900\"\n                                title=\"Cancel edit\"\n                              >\n                                <X className=\"w-4 h-4\" />\n                              </button>\n                            </>\n                          ) : (\n                            <>\n                              <button\n                                onClick={() => handleEditClick(payment)}\n                                className=\"text-blue-600 hover:text-blue-900\"\n                                title=\"Edit record\"\n                              >\n                                <Edit className=\"w-4 h-4\" />\n                              </button>\n                              {payment.has_updates && (\n                                <button\n                                  onClick={() => fetchHistory(payment.id)}\n                                  className=\"text-purple-600 hover:text-purple-900\"\n                                  title=\"View history\"\n                                >\n                                  <History className=\"w-4 h-4\" />\n                                </button>\n                              )}\n                              <a\n                                href={payment.receipt_url}\n                                target=\"_blank\"\n                                rel=\"noopener noreferrer\"\n                                className=\"text-gray-600 hover:text-gray-900\"\n                                title=\"View receipt\"\n                              >\n                                <Eye className=\"w-4 h-4\" />\n                              </a>\n                            </>\n                          )}\n                        </div>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Pagination */}\n            {totalPages > 1 && (\n              <div className=\"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6\">\n                <div className=\"flex-1 flex justify-between sm:hidden\">\n                  <button\n                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                    disabled={currentPage === 1}\n                    className=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    Previous\n                  </button>\n                  <button\n                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                    disabled={currentPage === totalPages}\n                    className=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    Next\n                  </button>\n                </div>\n                <div className=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n                  <div>\n                    <p className=\"text-sm text-gray-700\">\n                      Showing page <span className=\"font-medium\">{currentPage}</span> of{' '}\n                      <span className=\"font-medium\">{totalPages}</span>\n                    </p>\n                  </div>\n                  <div>\n                    <nav className=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\" aria-label=\"Pagination\">\n                      <button\n                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}\n                        disabled={currentPage === 1}\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        <ChevronLeft className=\"h-5 w-5\" />\n                      </button>\n                      <button\n                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}\n                        disabled={currentPage === totalPages}\n                        className=\"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\"\n                      >\n                        <ChevronRight className=\"h-5 w-5\" />\n                      </button>\n                    </nav>\n                  </div>\n                </div>\n              </div>\n            )}\n          </>\n        )}\n      </div>\n\n      {/* Edit Reason Modal */}\n      {editingPayment && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Update Reason</h3>\n            <textarea\n              value={updateReason}\n              onChange={(e) => setUpdateReason(e.target.value)}\n              placeholder=\"Please provide a reason for this update...\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              rows={3}\n            />\n            <div className=\"flex justify-end gap-2 mt-4\">\n              <button\n                onClick={handleCancelEdit}\n                className=\"px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleSaveEdit}\n                disabled={!updateReason.trim()}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Save Changes\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* History Modal */}\n      {showHistory && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h3 className=\"text-lg font-medium text-gray-900\">Payment History</h3>\n              <button\n                onClick={() => setShowHistory(null)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                <X className=\"w-6 h-6\" />\n              </button>\n            </div>\n\n            {historyData.length === 0 ? (\n              <p className=\"text-gray-500\">No history records found.</p>\n            ) : (\n              <div className=\"space-y-4\">\n                {historyData.map((record) => (\n                  <div key={record.id} className=\"border border-gray-200 rounded-lg p-4\">\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <div className=\"text-sm font-medium text-gray-900\">\n                        Field: {record.field_name}\n                      </div>\n                      <div className=\"text-xs text-gray-500\">\n                        {format(new Date(record.created_at), 'dd/MM/yyyy HH:mm')}\n                      </div>\n                    </div>\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Old Value:</span>\n                        <div className=\"text-gray-900 bg-red-50 px-2 py-1 rounded\">\n                          {record.old_value || 'N/A'}\n                        </div>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">New Value:</span>\n                        <div className=\"text-gray-900 bg-green-50 px-2 py-1 rounded\">\n                          {record.new_value || 'N/A'}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"mt-2 text-sm\">\n                      <span className=\"text-gray-500\">Updated by:</span>\n                      <span className=\"text-gray-900 ml-1\">{record.updated_by}</span>\n                    </div>\n                    {record.update_reason && (\n                      <div className=\"mt-2 text-sm\">\n                        <span className=\"text-gray-500\">Reason:</span>\n                        <div className=\"text-gray-900 bg-gray-50 px-2 py-1 rounded mt-1\">\n                          {record.update_reason}\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QACxD,aAAa;QACb,WAAW;QACX,QAAQ;QACR,QAAQ;QACR,WAAW;QACX,SAAS;IACX;IAEA,MAAM,gBAAgB;QACpB,WAAW;QACX,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,YAAY,QAAQ;gBAC1B,OAAO;gBACP,QAAQ;gBACR,WAAW;gBACX,GAAI,QAAQ,MAAM,IAAI;oBAAE,QAAQ,QAAQ,MAAM;gBAAC,CAAC;gBAChD,GAAI,QAAQ,MAAM,IAAI;oBAAE,QAAQ,QAAQ,MAAM;gBAAC,CAAC;gBAChD,GAAI,QAAQ,WAAW,IAAI;oBAAE,aAAa,QAAQ,WAAW;gBAAC,CAAC;gBAC/D,GAAI,QAAQ,SAAS,IAAI;oBAAE,WAAW,QAAQ,SAAS;gBAAC,CAAC;gBACzD,GAAI,QAAQ,SAAS,IAAI;oBAAE,WAAW,QAAQ,SAAS;gBAAC,CAAC;gBACzD,GAAI,QAAQ,OAAO,IAAI;oBAAE,SAAS,QAAQ,OAAO;gBAAC,CAAC;YACrD;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ;YACtD,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,YAAY,OAAO,IAAI;YACvB,cAAc,OAAO,UAAU,CAAC,UAAU;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAa;KAAQ;IAEzB,MAAM,qBAAqB,CAAC,KAA8B;QACxD,WAAW,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,IAAI,EAAE;YAAM,CAAC;QAC7C,eAAe,GAAG,qCAAqC;;IACzD;IAEA,MAAM,eAAe;QACnB,WAAW;YACT,aAAa;YACb,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,WAAW;YACX,SAAS;QACX;QACA,eAAe;IACjB;IAEA,MAAM,cAAc;QAClB,MAAM,UAAU;YAAC;YAAQ;YAAgB;YAAS;YAAU;YAAU;YAAU;YAAW;SAAQ;QACnG,MAAM,UAAU,SAAS,GAAG,CAAC,CAAA,UAAW;gBACtC,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,YAAY,GAAG;gBACvC,QAAQ,OAAO,EAAE,gBAAgB;gBACjC,QAAQ,OAAO,EAAE,QAAQ,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG;gBAC5F,QAAQ,eAAe;gBACvB,QAAQ,cAAc;gBACtB,QAAQ,cAAc;gBACtB,QAAQ,iBAAiB;gBACzB,QAAQ,KAAK,IAAI;aAClB;QAED,MAAM,aAAa;YAAC;eAAY;SAAQ,CACrC,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,CAAC,CAAA,QAAS,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAC/C,IAAI,CAAC;QAER,MAAM,OAAO,IAAI,KAAK;YAAC;SAAW,EAAE;YAAE,MAAM;QAAW;QACvD,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;QACvC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,YAAY,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,cAAc,IAAI,CAAC;QAClE,EAAE,KAAK;QACP,OAAO,GAAG,CAAC,eAAe,CAAC;IAC7B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;QACzD;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,kBAAkB,QAAQ,EAAE;QAC5B,YAAY;YACV,iBAAiB,QAAQ,eAAe;YACxC,cAAc,QAAQ,YAAY;YAClC,gBAAgB,QAAQ,cAAc;YACtC,gBAAgB,QAAQ,cAAc;YACtC,mBAAmB,QAAQ,iBAAiB;YAC5C,OAAO,QAAQ,KAAK;QACtB;QACA,gBAAgB;IAClB;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,kBAAkB,CAAC,aAAa,IAAI,IAAI;YAC3C,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,gBAAgB,UAAU,SAAS;YAC1D,kBAAkB;YAClB,YAAY,CAAC;YACb,gBAAgB;YAChB,gBAAgB,mBAAmB;;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;QAClB,YAAY,CAAC;QACb,gBAAgB;IAClB;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,gCAAgC,EAAE,WAAW;YAC3E,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,UAAU,MAAM,SAAS,IAAI;YACnC,eAAe;YACf,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe,CAAC;gCAC/B,WAAU;;kDAEV,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGhC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;YAOrC,6BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,WAAW;wCAC1B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;wCACjE,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAGd,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,SAAS;wCACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC/D,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAGd,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO,QAAQ,MAAM;wCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;;;;;;;;;;;;;0CAG5B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,OAAO,QAAQ,MAAM;wCACrB,UAAU,CAAC,IAAM,mBAAmB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC5D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAO,OAAM;0DAAS;;;;;;;;;;;;;;;;;;0CAG3B,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,SAAS;wCACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC/D,WAAU;;;;;;;;;;;;0CAGd,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAChE,8OAAC;wCACC,MAAK;wCACL,OAAO,QAAQ,OAAO;wCACtB,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC7D,WAAU;;;;;;;;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;0BACZ,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;2BAElC,SAAS,MAAM,KAAK,kBACtB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;yCAG/B;;sCACE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAM,WAAU;;kDACf,8OAAC;wCAAM,WAAU;kDACf,cAAA,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;8DAG/F,8OAAC;oDAAG,WAAU;8DAAiF;;;;;;;;;;;;;;;;;kDAKnG,8OAAC;wCAAM,WAAU;kDACd,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAG,WAAU;kEACX,mBAAmB,QAAQ,EAAE,iBAC5B,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,YAAY,IAAI;4DAChC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC/E,WAAU;;;;;mEAGZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,YAAY,GAAG;;;;;;kEAG3C,8OAAC;wDAAG,WAAU;kEACX,QAAQ,OAAO,EAAE,gBAAgB;;;;;;kEAEpC,8OAAC;wDAAG,WAAU;kEACX,QAAQ,OAAO,EAAE,QAAQ,GAAG,QAAQ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG;;;;;;kEAE/F,8OAAC;wDAAG,WAAU;kEACX,mBAAmB,QAAQ,EAAE,iBAC5B,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,eAAe,IAAI;4DACnC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DAC9F,WAAU;;;;;mEAGZ,CAAC,CAAC,EAAE,QAAQ,eAAe,CAAC,cAAc,IAAI;;;;;;kEAGlD,8OAAC;wDAAG,WAAU;kEACX,mBAAmB,QAAQ,EAAE,iBAC5B,8OAAC;4DACC,OAAO,SAAS,cAAc,IAAI;4DAClC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAAQ,CAAC;4DACxF,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAO;;;;;;8EACrB,8OAAC;oEAAO,OAAM;8EAAO;;;;;;8EACrB,8OAAC;oEAAO,OAAM;8EAAM;;;;;;8EACpB,8OAAC;oEAAO,OAAM;8EAAgB;;;;;;8EAC9B,8OAAC;oEAAO,OAAM;8EAAS;;;;;;;;;;;mEAGzB,oBAAoB,QAAQ,cAAc;;;;;;kEAG9C,8OAAC;wDAAG,WAAU;kEACX,mBAAmB,QAAQ,EAAE,iBAC5B,8OAAC;4DACC,OAAO,SAAS,cAAc,IAAI;4DAClC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAAQ,CAAC;4DACxF,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAY;;;;;;8EAC1B,8OAAC;oEAAO,OAAM;8EAAU;;;;;;8EACxB,8OAAC;oEAAO,OAAM;8EAAU;;;;;;;;;;;iFAG1B,8OAAC;4DAAK,WAAW,CAAC,yDAAyD,EAAE,eAAe,QAAQ,cAAc,GAAG;sEAClH,QAAQ,cAAc;;;;;;;;;;;kEAI7B,8OAAC;wDAAG,WAAU;kEACX,mBAAmB,QAAQ,EAAE,iBAC5B,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,iBAAiB,IAAI;4DACrC,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DAChG,WAAU;;;;;mEAGZ,CAAC,CAAC,EAAE,QAAQ,iBAAiB,CAAC,cAAc,IAAI;;;;;;kEAGpD,8OAAC;wDAAG,WAAU;kEACZ,cAAA,8OAAC;4DAAI,WAAU;sEACZ,mBAAmB,QAAQ,EAAE,iBAC5B;;kFACE,8OAAC;wEACC,SAAS;wEACT,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;kFAElB,8OAAC;wEACC,SAAS;wEACT,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;4EAAC,WAAU;;;;;;;;;;;;6FAIjB;;kFACE,8OAAC;wEACC,SAAS,IAAM,gBAAgB;wEAC/B,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;oEAEjB,QAAQ,WAAW,kBAClB,8OAAC;wEACC,SAAS,IAAM,aAAa,QAAQ,EAAE;wEACtC,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,wMAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;kFAGvB,8OAAC;wEACC,MAAM,QAAQ,WAAW;wEACzB,QAAO;wEACP,KAAI;wEACJ,WAAU;wEACV,OAAM;kFAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;+CA3HlB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;wBAwI1B,aAAa,mBACZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;4CACzD,UAAU,gBAAgB;4CAC1B,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;4CACzD,UAAU,gBAAgB;4CAC1B,WAAU;sDACX;;;;;;;;;;;;8CAIH,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDACC,cAAA,8OAAC;gDAAE,WAAU;;oDAAwB;kEACtB,8OAAC;wDAAK,WAAU;kEAAe;;;;;;oDAAmB;oDAAI;kEACnE,8OAAC;wDAAK,WAAU;kEAAe;;;;;;;;;;;;;;;;;sDAGnC,8OAAC;sDACC,cAAA,8OAAC;gDAAI,WAAU;gDAA4D,cAAW;;kEACpF,8OAAC;wDACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;wDACzD,UAAU,gBAAgB;wDAC1B,WAAU;kEAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAEzB,8OAAC;wDACC,SAAS,IAAM,eAAe,CAAA,OAAQ,KAAK,GAAG,CAAC,OAAO,GAAG;wDACzD,UAAU,gBAAgB;wDAC1B,WAAU;kEAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYzC,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BACC,OAAO;4BACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BAC/C,aAAY;4BACZ,WAAU;4BACV,MAAM;;;;;;sCAER,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU,CAAC,aAAa,IAAI;oCAC5B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,6BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIhB,YAAY,MAAM,KAAK,kBACtB,8OAAC;4BAAE,WAAU;sCAAgB;;;;;iDAE7B,8OAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDAAoC;wDACzC,OAAO,UAAU;;;;;;;8DAE3B,8OAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,UAAU,GAAG;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAI,WAAU;sEACZ,OAAO,SAAS,IAAI;;;;;;;;;;;;8DAGzB,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAI,WAAU;sEACZ,OAAO,SAAS,IAAI;;;;;;;;;;;;;;;;;;sDAI3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DAAsB,OAAO,UAAU;;;;;;;;;;;;wCAExD,OAAO,aAAa,kBACnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAI,WAAU;8DACZ,OAAO,aAAa;;;;;;;;;;;;;mCA/BnB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CrC", "debugId": null}}, {"offset": {"line": 2749, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/components/PendingFeesComponent.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { format } from 'date-fns'\nimport { AlertCircle, CreditCard, Search, Filter, Phone, Calendar } from 'lucide-react'\nimport { FeePayment, Student } from '@/types/database'\n\ninterface PendingFeeStudent extends Student {\n  totalPaid: number\n  totalPending: number\n  lastPaymentDate?: string\n  lastPaymentAmount?: number\n  pendingMonth?: number\n  pendingYear?: number\n  pendingReason?: string\n}\n\nexport default function PendingFeesComponent() {\n  const [pendingStudents, setPendingStudents] = useState<PendingFeeStudent[]>([])\n  const [loading, setLoading] = useState(true)\n  const [searchTerm, setSearchTerm] = useState('')\n  const [classFilter, setClassFilter] = useState('')\n  const [showQuickPayment, setShowQuickPayment] = useState<string | null>(null)\n  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1)\n  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear())\n  const [showMonthFilter, setShowMonthFilter] = useState(false)\n\n  const months = [\n    { value: 1, label: 'January' },\n    { value: 2, label: 'February' },\n    { value: 3, label: 'March' },\n    { value: 4, label: 'April' },\n    { value: 5, label: 'May' },\n    { value: 6, label: 'June' },\n    { value: 7, label: 'July' },\n    { value: 8, label: 'August' },\n    { value: 9, label: 'September' },\n    { value: 10, label: 'October' },\n    { value: 11, label: 'November' },\n    { value: 12, label: 'December' }\n  ]\n\n  const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i)\n\n  const fetchPendingFees = async () => {\n    setLoading(true)\n    try {\n      let url = '/api/pending-fees'\n      if (showMonthFilter) {\n        url += `?month=${selectedMonth}&year=${selectedYear}`\n      }\n\n      const response = await fetch(url)\n      if (!response.ok) throw new Error('Failed to fetch pending fees')\n      const studentsWithPending: PendingFeeStudent[] = await response.json()\n      setPendingStudents(studentsWithPending)\n    } catch (error) {\n      console.error('Error fetching pending fees:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  useEffect(() => {\n    fetchPendingFees()\n  }, [selectedMonth, selectedYear, showMonthFilter])\n\n  const filteredStudents = pendingStudents.filter(student => {\n    const matchesSearch = student.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         student.father_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         student.mother_name.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesClass = !classFilter || \n                        (student.class?.name.toLowerCase().includes(classFilter.toLowerCase()))\n    \n    return matchesSearch && matchesClass\n  })\n\n  const handleQuickPayment = (studentId: string) => {\n    setShowQuickPayment(studentId)\n  }\n\n  const sendWhatsAppReminder = (student: PendingFeeStudent) => {\n    const message = `Dear Parent,\n\nThis is a gentle reminder that the fee payment for ${student.student_name} (Class: ${student.class?.name} ${student.class?.section}) is pending.\n\nOutstanding Amount: ₹${student.totalPending.toLocaleString()}\n${student.lastPaymentDate ? `Last Payment: ₹${student.lastPaymentAmount?.toLocaleString()} on ${format(new Date(student.lastPaymentDate), 'dd/MM/yyyy')}` : 'No previous payments found'}\n\nPlease make the payment at your earliest convenience.\n\nThank you,\n${process.env.NEXT_PUBLIC_SCHOOL_NAME || 'First Step School'}`\n\n    const phoneNumber = student.father_mobile || student.mother_mobile\n    if (phoneNumber) {\n      const whatsappUrl = `https://wa.me/${phoneNumber.replace(/\\D/g, '')}?text=${encodeURIComponent(message)}`\n      window.open(whatsappUrl, '_blank')\n    }\n  }\n\n  const totalPendingAmount = filteredStudents.reduce((sum, student) => sum + student.totalPending, 0)\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-4 lg:p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        {/* Header with Summary */}\n        <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 lg:p-6\">\n          <div className=\"flex flex-col sm:flex-row sm:items-center gap-3\">\n            <AlertCircle className=\"w-6 h-6 text-red-600 flex-shrink-0\" />\n            <div>\n              <h3 className=\"text-lg lg:text-xl font-medium text-red-900\">\n                Pending Fees Summary\n                {showMonthFilter && (\n                  <span className=\"block sm:inline text-sm font-normal sm:ml-2 mt-1 sm:mt-0\">\n                    for {months.find(m => m.value === selectedMonth)?.label} {selectedYear}\n                  </span>\n                )}\n              </h3>\n              <p className=\"text-red-700 text-sm lg:text-base\">\n                {filteredStudents.length} students have pending fees totaling ₹{totalPendingAmount.toLocaleString()}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Controls */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 lg:p-6\">\n          <div className=\"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4\">\n            {/* Month/Year Filter Toggle */}\n            <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto\">\n              <button\n                onClick={() => setShowMonthFilter(!showMonthFilter)}\n                className={`flex items-center gap-2 px-4 py-2 rounded-md border transition-colors text-sm ${\n                  showMonthFilter\n                    ? 'bg-blue-600 text-white border-blue-600'\n                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'\n                }`}\n              >\n                <Calendar className=\"w-4 h-4\" />\n                {showMonthFilter ? 'Show All Pending' : 'Filter by Month'}\n              </button>\n\n              {showMonthFilter && (\n                <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-2\">\n                  <select\n                    value={selectedMonth}\n                    onChange={(e) => setSelectedMonth(parseInt(e.target.value))}\n                    className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 text-sm\"\n                  >\n                    {months.map(month => (\n                      <option key={month.value} value={month.value}>\n                    {month.label}\n                  </option>\n                ))}\n              </select>\n\n              <select\n                value={selectedYear}\n                onChange={(e) => setSelectedYear(parseInt(e.target.value))}\n                className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n              >\n                {years.map(year => (\n                  <option key={year} value={year}>\n                    {year}\n                  </option>\n                ))}\n              </select>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Search and Filter */}\n      <div className=\"flex flex-col sm:flex-row gap-4\">\n        <div className=\"flex-1\">\n          <div className=\"relative\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search by student name or parent name...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n            />\n          </div>\n        </div>\n        <div className=\"w-full sm:w-48\">\n          <input\n            type=\"text\"\n            placeholder=\"Filter by class...\"\n            value={classFilter}\n            onChange={(e) => setClassFilter(e.target.value)}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900\"\n          />\n        </div>\n      </div>\n\n      {/* Pending Students List */}\n      <div className=\"bg-white border border-gray-200 rounded-lg overflow-hidden\">\n        {loading ? (\n          <div className=\"p-8 text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-500\">Loading pending fees...</p>\n          </div>\n        ) : filteredStudents.length === 0 ? (\n          <div className=\"p-8 text-center\">\n            <AlertCircle className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-500\">\n              {pendingStudents.length === 0 \n                ? \"Great! No students have pending fees.\" \n                : \"No students found matching your search criteria.\"\n              }\n            </p>\n          </div>\n        ) : (\n          <div className=\"overflow-x-auto\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Student Details\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Class\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Total Paid\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Pending Amount\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Last Payment\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Contact\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredStudents.map((student) => (\n                  <tr key={student.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900\">{student.student_name}</div>\n                        <div className=\"text-sm text-gray-500\">\n                          Father: {student.father_name}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          Mother: {student.mother_name}\n                        </div>\n                        {student.pendingReason && (\n                          <div className=\"text-xs text-red-600 mt-1 bg-red-50 px-2 py-1 rounded\">\n                            {student.pendingReason}\n                          </div>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {student.class ? `${student.class.name} ${student.class.section}` : 'N/A'}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      ₹{student.totalPaid.toLocaleString()}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className=\"text-sm font-medium text-red-600\">\n                        ₹{student.totalPending.toLocaleString()}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {student.lastPaymentDate ? (\n                        <div>\n                          <div>₹{student.lastPaymentAmount?.toLocaleString()}</div>\n                          <div className=\"text-xs text-gray-500\">\n                            {format(new Date(student.lastPaymentDate), 'dd/MM/yyyy')}\n                          </div>\n                        </div>\n                      ) : (\n                        <span className=\"text-gray-400\">No payments</span>\n                      )}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      <div>\n                        {student.father_mobile && (\n                          <div className=\"text-xs\">F: {student.father_mobile}</div>\n                        )}\n                        {student.mother_mobile && (\n                          <div className=\"text-xs\">M: {student.mother_mobile}</div>\n                        )}\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      <div className=\"flex gap-2\">\n                        <button\n                          onClick={() => handleQuickPayment(student.id)}\n                          className=\"text-blue-600 hover:text-blue-900 flex items-center gap-1\"\n                        >\n                          <CreditCard className=\"w-4 h-4\" />\n                          Collect\n                        </button>\n                        {(student.father_mobile || student.mother_mobile) && (\n                          <button\n                            onClick={() => sendWhatsAppReminder(student)}\n                            className=\"text-green-600 hover:text-green-900 flex items-center gap-1\"\n                          >\n                            <Phone className=\"w-4 h-4\" />\n                            Remind\n                          </button>\n                        )}\n                      </div>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n\n      {/* Quick Payment Modal */}\n      {showQuickPayment && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Fee Collection</h3>\n            <p className=\"text-sm text-gray-600 mb-4\">\n              This will redirect you to the fee collection form with the student pre-selected.\n            </p>\n            <div className=\"flex gap-3\">\n              <button\n                onClick={() => {\n                  // Here you would typically navigate to the fee collection tab with the student pre-selected\n                  // For now, we'll just close the modal\n                  setShowQuickPayment(null)\n                  // You could also emit an event or use a callback to switch tabs\n                }}\n                className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700\"\n              >\n                Go to Collection\n              </button>\n              <button\n                onClick={() => setShowQuickPayment(null)}\n                className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400\"\n              >\n                Cancel\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n      </div>\n    </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAiBe,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,IAAI,OAAO,QAAQ,KAAK;IACnF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,IAAI,OAAO,WAAW;IAC/E,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,SAAS;QACb;YAAE,OAAO;YAAG,OAAO;QAAU;QAC7B;YAAE,OAAO;YAAG,OAAO;QAAW;QAC9B;YAAE,OAAO;YAAG,OAAO;QAAQ;QAC3B;YAAE,OAAO;YAAG,OAAO;QAAQ;QAC3B;YAAE,OAAO;YAAG,OAAO;QAAM;QACzB;YAAE,OAAO;YAAG,OAAO;QAAO;QAC1B;YAAE,OAAO;YAAG,OAAO;QAAO;QAC1B;YAAE,OAAO;YAAG,OAAO;QAAS;QAC5B;YAAE,OAAO;YAAG,OAAO;QAAY;QAC/B;YAAE,OAAO;YAAI,OAAO;QAAU;QAC9B;YAAE,OAAO;YAAI,OAAO;QAAW;QAC/B;YAAE,OAAO;YAAI,OAAO;QAAW;KAChC;IAED,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAE,GAAG,CAAC,GAAG,IAAM,IAAI,OAAO,WAAW,KAAK,IAAI;IAEjF,MAAM,mBAAmB;QACvB,WAAW;QACX,IAAI;YACF,IAAI,MAAM;YACV,IAAI,iBAAiB;gBACnB,OAAO,CAAC,OAAO,EAAE,cAAc,MAAM,EAAE,cAAc;YACvD;YAEA,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,sBAA2C,MAAM,SAAS,IAAI;YACpE,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAe;QAAc;KAAgB;IAEjD,MAAM,mBAAmB,gBAAgB,MAAM,CAAC,CAAA;QAC9C,MAAM,gBAAgB,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACnE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEtF,MAAM,eAAe,CAAC,eACD,QAAQ,KAAK,EAAE,KAAK,cAAc,SAAS,YAAY,WAAW;QAEvF,OAAO,iBAAiB;IAC1B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;IACtB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,UAAU,CAAC;;mDAE8B,EAAE,QAAQ,YAAY,CAAC,SAAS,EAAE,QAAQ,KAAK,EAAE,KAAK,CAAC,EAAE,QAAQ,KAAK,EAAE,QAAQ;;qBAE9G,EAAE,QAAQ,YAAY,CAAC,cAAc,GAAG;AAC7D,EAAE,QAAQ,eAAe,GAAG,CAAC,eAAe,EAAE,QAAQ,iBAAiB,EAAE,iBAAiB,IAAI,EAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,eAAe,GAAG,eAAe,GAAG,6BAA6B;;;;;AAKzL,EAAE,yDAAuC,qBAAqB;QAE1D,MAAM,cAAc,QAAQ,aAAa,IAAI,QAAQ,aAAa;QAClE,IAAI,aAAa;YACf,MAAM,cAAc,CAAC,cAAc,EAAE,YAAY,OAAO,CAAC,OAAO,IAAI,MAAM,EAAE,mBAAmB,UAAU;YACzG,OAAO,IAAI,CAAC,aAAa;QAC3B;IACF;IAEA,MAAM,qBAAqB,iBAAiB,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,QAAQ,YAAY,EAAE;IAEjG,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;4CAA8C;4CAEzD,iCACC,8OAAC;gDAAK,WAAU;;oDAA2D;oDACpE,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,gBAAgB;oDAAM;oDAAE;;;;;;;;;;;;;kDAIhE,8OAAC;wCAAE,WAAU;;4CACV,iBAAiB,MAAM;4CAAC;4CAAuC,mBAAmB,cAAc;;;;;;;;;;;;;;;;;;;;;;;;8BAOzG,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAEb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,mBAAmB,CAAC;wCACnC,WAAW,CAAC,8EAA8E,EACxF,kBACI,2CACA,2DACJ;;0DAEF,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,kBAAkB,qBAAqB;;;;;;;oCAGzC,iCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;gDACzD,WAAU;0DAET,OAAO,GAAG,CAAC,CAAA,sBACV,8OAAC;wDAAyB,OAAO,MAAM,KAAK;kEAC7C,MAAM,KAAK;uDADG,MAAM,KAAK;;;;;;;;;;0DAMhC,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxD,WAAU;0DAET,MAAM,GAAG,CAAC,CAAA,qBACT,8OAAC;wDAAkB,OAAO;kEACvB;uDADU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC7C,WAAU;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;sCACZ,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;uCAElC,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAE,WAAU;kDACV,gBAAgB,MAAM,KAAK,IACxB,0CACA;;;;;;;;;;;qDAKR,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CAAM,WAAU;sDACf,cAAA,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;kEAG/F,8OAAC;wDAAG,WAAU;kEAAiF;;;;;;;;;;;;;;;;;sDAKnG,8OAAC;4CAAM,WAAU;sDACd,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;oDAAoB,WAAU;;sEAC7B,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;kFACC,8OAAC;wEAAI,WAAU;kFAAqC,QAAQ,YAAY;;;;;;kFACxE,8OAAC;wEAAI,WAAU;;4EAAwB;4EAC5B,QAAQ,WAAW;;;;;;;kFAE9B,8OAAC;wEAAI,WAAU;;4EAAwB;4EAC5B,QAAQ,WAAW;;;;;;;oEAE7B,QAAQ,aAAa,kBACpB,8OAAC;wEAAI,WAAU;kFACZ,QAAQ,aAAa;;;;;;;;;;;;;;;;;sEAK9B,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK,GAAG,GAAG,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,OAAO,EAAE,GAAG;;;;;;sEAEtE,8OAAC;4DAAG,WAAU;;gEAAoD;gEAC9D,QAAQ,SAAS,CAAC,cAAc;;;;;;;sEAEpC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAK,WAAU;;oEAAmC;oEAC/C,QAAQ,YAAY,CAAC,cAAc;;;;;;;;;;;;sEAGzC,8OAAC;4DAAG,WAAU;sEACX,QAAQ,eAAe,iBACtB,8OAAC;;kFACC,8OAAC;;4EAAI;4EAAE,QAAQ,iBAAiB,EAAE;;;;;;;kFAClC,8OAAC;wEAAI,WAAU;kFACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,eAAe,GAAG;;;;;;;;;;;qFAI/C,8OAAC;gEAAK,WAAU;0EAAgB;;;;;;;;;;;sEAGpC,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;;oEACE,QAAQ,aAAa,kBACpB,8OAAC;wEAAI,WAAU;;4EAAU;4EAAI,QAAQ,aAAa;;;;;;;oEAEnD,QAAQ,aAAa,kBACpB,8OAAC;wEAAI,WAAU;;4EAAU;4EAAI,QAAQ,aAAa;;;;;;;;;;;;;;;;;;sEAIxD,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,SAAS,IAAM,mBAAmB,QAAQ,EAAE;wEAC5C,WAAU;;0FAEV,8OAAC,kNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;4EAAY;;;;;;;oEAGnC,CAAC,QAAQ,aAAa,IAAI,QAAQ,aAAa,mBAC9C,8OAAC;wEACC,SAAS,IAAM,qBAAqB;wEACpC,WAAU;;0FAEV,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAAY;;;;;;;;;;;;;;;;;;;mDAhE9B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;wBA+E9B,kCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;oDACP,4FAA4F;oDAC5F,sCAAsC;oDACtC,oBAAoB;gDACpB,gEAAgE;gDAClE;gDACA,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,oBAAoB;gDACnC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWf", "debugId": null}}, {"offset": {"line": 3553, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/components/AttendanceManagement.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { format } from 'date-fns'\nimport { Calendar, Users, MessageSquare, CheckCircle, XCircle, Save, Send, Filter, Search, RotateCcw, UserCheck, UserX } from 'lucide-react'\nimport DatePicker from 'react-datepicker'\nimport { Student, Attendance } from '@/types/database'\nimport \"react-datepicker/dist/react-datepicker.css\"\n\ninterface StudentWithAttendance extends Student {\n  attendance?: Attendance\n}\n\ninterface AttendanceStats {\n  totalStudents: number\n  presentCount: number\n  absentCount: number\n  attendancePercentage: number\n}\n\ninterface ClassInfo {\n  id: string\n  name: string\n  section: string\n}\n\nexport default function AttendanceManagement() {\n  const [selectedDate, setSelectedDate] = useState<Date>(new Date())\n  const [students, setStudents] = useState<StudentWithAttendance[]>([])\n  const [filteredStudents, setFilteredStudents] = useState<StudentWithAttendance[]>([])\n  const [loading, setLoading] = useState(false)\n  const [saving, setSaving] = useState(false)\n  const [stats, setStats] = useState<AttendanceStats | null>(null)\n  const [messageText, setMessageText] = useState('')\n  const [sendingMessages, setSendingMessages] = useState(false)\n\n  // New state for class selection and filtering\n  const [classes, setClasses] = useState<ClassInfo[]>([])\n  const [selectedClass, setSelectedClass] = useState<string>('all')\n  const [searchTerm, setSearchTerm] = useState('')\n  const [attendanceFilter, setAttendanceFilter] = useState<'all' | 'present' | 'absent' | 'unmarked'>('all')\n  const [bulkAction, setBulkAction] = useState<'present' | 'absent' | null>(null)\n  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(new Set())\n\n  // Load classes on component mount\n  useEffect(() => {\n    loadClasses()\n  }, [])\n\n  // Load students with attendance for selected date and class\n  useEffect(() => {\n    loadStudentsWithAttendance()\n  }, [selectedDate, selectedClass])\n\n  // Filter students based on search term and attendance filter\n  useEffect(() => {\n    filterStudents()\n  }, [students, searchTerm, attendanceFilter])\n\n  const loadClasses = async () => {\n    try {\n      const response = await fetch('/api/classes-with-names')\n      if (!response.ok) throw new Error('Failed to fetch classes')\n      const classesData = await response.json()\n      setClasses(classesData)\n    } catch (error) {\n      console.error('Error loading classes:', error)\n    }\n  }\n\n  const loadStudentsWithAttendance = async () => {\n    setLoading(true)\n    try {\n      const dateStr = format(selectedDate, 'yyyy-MM-dd')\n      let url = `/api/attendance/students?date=${dateStr}`\n      if (selectedClass !== 'all') {\n        url += `&class=${selectedClass}`\n      }\n\n      const response = await fetch(url)\n      if (!response.ok) throw new Error('Failed to fetch students')\n\n      const studentsData = await response.json()\n      setStudents(studentsData)\n\n      // Load statistics\n      const statsResponse = await fetch(`/api/attendance/statistics?date=${dateStr}${selectedClass !== 'all' ? `&class=${selectedClass}` : ''}`)\n      if (statsResponse.ok) {\n        const statsData = await statsResponse.json()\n        setStats(statsData)\n      }\n    } catch (error) {\n      console.error('Error loading students:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const filterStudents = () => {\n    let filtered = students\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(student =>\n        student.student_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        student.father_name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        student.mother_name.toLowerCase().includes(searchTerm.toLowerCase())\n      )\n    }\n\n    // Filter by attendance status\n    if (attendanceFilter !== 'all') {\n      filtered = filtered.filter(student => {\n        if (attendanceFilter === 'unmarked') {\n          return !student.attendance\n        }\n        return student.attendance?.status === attendanceFilter\n      })\n    }\n\n    setFilteredStudents(filtered)\n  }\n\n  const handleAttendanceChange = (studentId: string, status: 'present' | 'absent') => {\n    setStudents(prev => prev.map(student => {\n      if (student.id === studentId) {\n        return {\n          ...student,\n          attendance: {\n            ...student.attendance,\n            student_id: studentId,\n            attendance_date: format(selectedDate, 'yyyy-MM-dd'),\n            status,\n            marked_by: 'admin',\n            id: student.attendance?.id || '',\n            created_at: student.attendance?.created_at || '',\n            updated_at: student.attendance?.updated_at || ''\n          }\n        }\n      }\n      return student\n    }))\n  }\n\n  const handleStudentSelection = (studentId: string, checked: boolean) => {\n    setSelectedStudents(prev => {\n      const newSet = new Set(prev)\n      if (checked) {\n        newSet.add(studentId)\n      } else {\n        newSet.delete(studentId)\n      }\n      return newSet\n    })\n  }\n\n  const handleSelectAll = (checked: boolean) => {\n    if (checked) {\n      setSelectedStudents(new Set(filteredStudents.map(s => s.id)))\n    } else {\n      setSelectedStudents(new Set())\n    }\n  }\n\n  const applyBulkAction = (status: 'present' | 'absent') => {\n    setStudents(prev => prev.map(student => {\n      if (selectedStudents.has(student.id)) {\n        return {\n          ...student,\n          attendance: {\n            ...student.attendance,\n            student_id: student.id,\n            attendance_date: format(selectedDate, 'yyyy-MM-dd'),\n            status,\n            marked_by: 'admin',\n            id: student.attendance?.id || '',\n            created_at: student.attendance?.created_at || '',\n            updated_at: student.attendance?.updated_at || ''\n          }\n        }\n      }\n      return student\n    }))\n    setSelectedStudents(new Set())\n  }\n\n  const markAllPresent = () => {\n    setStudents(prev => prev.map(student => ({\n      ...student,\n      attendance: {\n        ...student.attendance,\n        student_id: student.id,\n        attendance_date: format(selectedDate, 'yyyy-MM-dd'),\n        status: 'present' as const,\n        marked_by: 'admin',\n        id: student.attendance?.id || '',\n        created_at: student.attendance?.created_at || '',\n        updated_at: student.attendance?.updated_at || ''\n      }\n    })))\n  }\n\n  const resetAttendance = () => {\n    setStudents(prev => prev.map(student => ({\n      ...student,\n      attendance: undefined\n    })))\n  }\n\n  const saveAttendance = async () => {\n    setSaving(true)\n    try {\n      const attendanceList = students.map(student => ({\n        student_id: student.id,\n        attendance_date: format(selectedDate, 'yyyy-MM-dd'),\n        status: student.attendance?.status || 'absent',\n        marked_by: 'admin'\n      }))\n\n      const response = await fetch('/api/attendance/bulk', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ attendanceList }),\n      })\n\n      if (!response.ok) throw new Error('Failed to save attendance')\n\n      alert('Attendance saved successfully!')\n      await loadStudentsWithAttendance() // Reload to get updated data\n    } catch (error) {\n      console.error('Error saving attendance:', error)\n      alert('Failed to save attendance')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const sendMessagesToAbsentStudents = async () => {\n    if (!messageText.trim()) {\n      alert('Please enter a message to send')\n      return\n    }\n\n    setSendingMessages(true)\n    try {\n      const absentStudents = students.filter(student => student.attendance?.status === 'absent')\n      \n      if (absentStudents.length === 0) {\n        alert('No absent students to send messages to')\n        return\n      }\n\n      const messages = []\n      for (const student of absentStudents) {\n        // Send to father if available\n        if (student.father_mobile) {\n          messages.push({\n            student_id: student.id,\n            attendance_date: format(selectedDate, 'yyyy-MM-dd'),\n            message_content: messageText,\n            recipient_type: 'father',\n            recipient_number: student.father_mobile\n          })\n        }\n        \n        // Send to mother if available and different from father\n        if (student.mother_mobile && student.mother_mobile !== student.father_mobile) {\n          messages.push({\n            student_id: student.id,\n            attendance_date: format(selectedDate, 'yyyy-MM-dd'),\n            message_content: messageText,\n            recipient_type: 'mother',\n            recipient_number: student.mother_mobile\n          })\n        }\n      }\n\n      // Save messages (in a real app, you'd integrate with SMS service)\n      for (const message of messages) {\n        await fetch('/api/attendance/messages', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(message),\n        })\n      }\n\n      alert(`Messages queued for ${absentStudents.length} absent students`)\n      setMessageText('')\n    } catch (error) {\n      console.error('Error sending messages:', error)\n      alert('Failed to send messages')\n    } finally {\n      setSendingMessages(false)\n    }\n  }\n\n  const presentCount = students.filter(s => s.attendance?.status === 'present').length\n  const absentCount = students.filter(s => s.attendance?.status === 'absent').length\n  const unmarkedCount = students.filter(s => !s.attendance?.status).length\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-4 lg:p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        {/* Header with Controls */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 lg:p-6\">\n          <div className=\"flex flex-col space-y-4\">\n            <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n              <div>\n                <h2 className=\"text-xl lg:text-2xl font-semibold text-gray-900 mb-2\">Attendance Management</h2>\n                <p className=\"text-gray-600\">Mark attendance for students</p>\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row items-start sm:items-center gap-3\">\n                <div className=\"flex items-center gap-2\">\n                  <Calendar className=\"w-5 h-5 text-gray-500\" />\n                  <DatePicker\n                    selected={selectedDate}\n                    onChange={(date: Date | null) => date && setSelectedDate(date)}\n                    dateFormat=\"yyyy-MM-dd\"\n                    className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n                    maxDate={new Date()}\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Filters and Controls */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n              {/* Class Selection */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Class</label>\n                <select\n                  value={selectedClass}\n                  onChange={(e) => setSelectedClass(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n                >\n                  <option value=\"all\">All Classes</option>\n                  {classes.map((cls) => (\n                    <option key={cls.id} value={cls.id}>\n                      {cls.name} - {cls.section}\n                    </option>\n                  ))}\n                </select>\n              </div>\n\n              {/* Search */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Search Students</label>\n                <div className=\"relative\">\n                  <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n                  <input\n                    type=\"text\"\n                    placeholder=\"Search by name...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n                  />\n                </div>\n              </div>\n\n              {/* Attendance Filter */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Filter by Status</label>\n                <select\n                  value={attendanceFilter}\n                  onChange={(e) => setAttendanceFilter(e.target.value as any)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n                >\n                  <option value=\"all\">All Students</option>\n                  <option value=\"present\">Present</option>\n                  <option value=\"absent\">Absent</option>\n                  <option value=\"unmarked\">Unmarked</option>\n                </select>\n              </div>\n\n              {/* Quick Actions */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">Quick Actions</label>\n                <div className=\"flex gap-2\">\n                  <button\n                    onClick={markAllPresent}\n                    className=\"flex-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm\"\n                    disabled={loading}\n                  >\n                    <UserCheck className=\"w-4 h-4 mx-auto\" />\n                  </button>\n                  <button\n                    onClick={resetAttendance}\n                    className=\"flex-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm\"\n                    disabled={loading}\n                  >\n                    <RotateCcw className=\"w-4 h-4 mx-auto\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Statistics */}\n        {stats && (\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n            <div className=\"bg-white rounded-lg shadow-sm border p-4\">\n              <div className=\"flex items-center\">\n                <Users className=\"w-6 h-6 lg:w-8 lg:h-8 text-blue-500\" />\n                <div className=\"ml-3\">\n                  <p className=\"text-xs lg:text-sm font-medium text-gray-500\">Total Students</p>\n                  <p className=\"text-lg lg:text-2xl font-semibold text-gray-900\">{stats.totalStudents}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border p-4\">\n              <div className=\"flex items-center\">\n                <CheckCircle className=\"w-6 h-6 lg:w-8 lg:h-8 text-green-500\" />\n                <div className=\"ml-3\">\n                  <p className=\"text-xs lg:text-sm font-medium text-gray-500\">Present</p>\n                  <p className=\"text-lg lg:text-2xl font-semibold text-gray-900\">{presentCount}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border p-4\">\n              <div className=\"flex items-center\">\n                <XCircle className=\"w-6 h-6 lg:w-8 lg:h-8 text-red-500\" />\n                <div className=\"ml-3\">\n                  <p className=\"text-xs lg:text-sm font-medium text-gray-500\">Absent</p>\n                  <p className=\"text-lg lg:text-2xl font-semibold text-gray-900\">{absentCount}</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-lg shadow-sm border p-4\">\n              <div className=\"flex items-center\">\n                <Calendar className=\"w-6 h-6 lg:w-8 lg:h-8 text-yellow-500\" />\n                <div className=\"ml-3\">\n                  <p className=\"text-xs lg:text-sm font-medium text-gray-500\">Unmarked</p>\n                  <p className=\"text-lg lg:text-2xl font-semibold text-gray-900\">{unmarkedCount}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n        {/* Bulk Actions and Save */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-4 lg:p-6\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4\">\n            <div>\n              <h3 className=\"text-lg font-medium text-gray-900\">Actions</h3>\n              <p className=\"text-sm text-gray-600\">\n                {selectedStudents.size > 0\n                  ? `${selectedStudents.size} students selected`\n                  : unmarkedCount > 0\n                    ? `${unmarkedCount} students not marked yet`\n                    : 'All students marked'\n                }\n              </p>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-3\">\n              {selectedStudents.size > 0 && (\n                <>\n                  <button\n                    onClick={() => applyBulkAction('present')}\n                    className=\"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm\"\n                  >\n                    Mark Selected Present\n                  </button>\n                  <button\n                    onClick={() => applyBulkAction('absent')}\n                    className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm\"\n                  >\n                    Mark Selected Absent\n                  </button>\n                </>\n              )}\n\n              <button\n                onClick={saveAttendance}\n                disabled={saving}\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2 text-sm\"\n              >\n                <Save className=\"w-4 h-4\" />\n                {saving ? 'Saving...' : 'Save Attendance'}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Student List */}\n        <div className=\"bg-white rounded-lg shadow-sm border\">\n          <div className=\"p-4 lg:p-6 border-b\">\n            <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n              <div>\n                <h3 className=\"text-lg font-medium text-gray-900\">Students</h3>\n                <p className=\"text-sm text-gray-600\">\n                  {filteredStudents.length} of {students.length} students\n                  {selectedClass !== 'all' && ` in selected class`}\n                </p>\n              </div>\n\n              {filteredStudents.length > 0 && (\n                <div className=\"flex items-center gap-2\">\n                  <input\n                    type=\"checkbox\"\n                    checked={selectedStudents.size === filteredStudents.length && filteredStudents.length > 0}\n                    onChange={(e) => handleSelectAll(e.target.checked)}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-600\">Select All</span>\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"p-4 lg:p-6\">\n            {loading ? (\n              <div className=\"text-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n                <p className=\"mt-2 text-gray-600\">Loading students...</p>\n              </div>\n            ) : filteredStudents.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <Users className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                <p className=\"text-gray-600\">\n                  {searchTerm || attendanceFilter !== 'all'\n                    ? 'No students match your filters'\n                    : 'No students found'\n                  }\n                </p>\n              </div>\n            ) : (\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n                {filteredStudents.map((student) => (\n                  <div\n                    key={student.id}\n                    className={`border rounded-lg p-4 transition-all hover:shadow-md ${\n                      student.attendance?.status === 'present'\n                        ? 'border-green-200 bg-green-50'\n                        : student.attendance?.status === 'absent'\n                        ? 'border-red-200 bg-red-50'\n                        : 'border-gray-200 bg-white hover:bg-gray-50'\n                    }`}\n                  >\n                    <div className=\"flex items-start justify-between mb-3\">\n                      <div className=\"flex items-start gap-3\">\n                        <input\n                          type=\"checkbox\"\n                          checked={selectedStudents.has(student.id)}\n                          onChange={(e) => handleStudentSelection(student.id, e.target.checked)}\n                          className=\"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                        />\n                        <div>\n                          <h4 className=\"font-medium text-gray-900 text-sm lg:text-base\">{student.student_name}</h4>\n                          <p className=\"text-xs lg:text-sm text-gray-600\">\n                            Class: {student.class?.name ? `${student.class.name} - ${student.class.section}` : student.class_id}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">Father: {student.father_name}</p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        {student.attendance?.status === 'present' && (\n                          <CheckCircle className=\"w-5 h-5 text-green-500\" />\n                        )}\n                        {student.attendance?.status === 'absent' && (\n                          <XCircle className=\"w-5 h-5 text-red-500\" />\n                        )}\n                      </div>\n                    </div>\n\n                    <div className=\"flex gap-2\">\n                      <button\n                        onClick={() => handleAttendanceChange(student.id, 'present')}\n                        className={`flex-1 px-3 py-2 rounded-md text-xs lg:text-sm font-medium transition-colors ${\n                          student.attendance?.status === 'present'\n                            ? 'bg-green-600 text-white'\n                            : 'bg-gray-100 text-gray-700 hover:bg-green-100 hover:text-green-700'\n                        }`}\n                      >\n                        <CheckCircle className=\"w-4 h-4 mx-auto lg:hidden\" />\n                        <span className=\"hidden lg:inline\">Present</span>\n                      </button>\n                      <button\n                        onClick={() => handleAttendanceChange(student.id, 'absent')}\n                        className={`flex-1 px-3 py-2 rounded-md text-xs lg:text-sm font-medium transition-colors ${\n                          student.attendance?.status === 'absent'\n                            ? 'bg-red-600 text-white'\n                            : 'bg-gray-100 text-gray-700 hover:bg-red-100 hover:text-red-700'\n                        }`}\n                      >\n                        <XCircle className=\"w-4 h-4 mx-auto lg:hidden\" />\n                        <span className=\"hidden lg:inline\">Absent</span>\n                      </button>\n                    </div>\n\n                    {student.attendance?.status === 'absent' && (\n                      <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                        <div className=\"text-xs text-gray-600 space-y-1\">\n                          <p>📞 Father: {student.father_mobile || 'N/A'}</p>\n                          <p>📞 Mother: {student.mother_mobile || 'N/A'}</p>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Messaging Section */}\n        {absentCount > 0 && (\n          <div className=\"bg-white rounded-lg shadow-sm border p-4 lg:p-6\">\n            <div className=\"flex items-center gap-2 mb-4\">\n              <MessageSquare className=\"w-5 h-5 text-blue-500\" />\n              <h3 className=\"text-lg font-medium text-gray-900\">Send Message to Absent Students</h3>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"message\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Message Content\n                </label>\n                <textarea\n                  id=\"message\"\n                  value={messageText}\n                  onChange={(e) => setMessageText(e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm\"\n                  placeholder=\"Enter message to send to parents of absent students...\"\n                />\n              </div>\n\n              <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n                <p className=\"text-sm text-gray-600\">\n                  Will send to {absentCount} absent students' parents\n                </p>\n                <button\n                  onClick={sendMessagesToAbsentStudents}\n                  disabled={sendingMessages || !messageText.trim()}\n                  className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2 text-sm\"\n                >\n                  <Send className=\"w-4 h-4\" />\n                  {sendingMessages ? 'Sending...' : 'Send Messages'}\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;;AA0Be,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,EAAE;IACpE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,EAAE;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,8CAA8C;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IACpG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAC1E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAE1E,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAc;KAAc;IAEhC,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAU;QAAY;KAAiB;IAE3C,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAClC,MAAM,cAAc,MAAM,SAAS,IAAI;YACvC,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,6BAA6B;QACjC,WAAW;QACX,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;YACrC,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS;YACpD,IAAI,kBAAkB,OAAO;gBAC3B,OAAO,CAAC,OAAO,EAAE,eAAe;YAClC;YAEA,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM,eAAe,MAAM,SAAS,IAAI;YACxC,YAAY;YAEZ,kBAAkB;YAClB,MAAM,gBAAgB,MAAM,MAAM,CAAC,gCAAgC,EAAE,UAAU,kBAAkB,QAAQ,CAAC,OAAO,EAAE,eAAe,GAAG,IAAI;YACzI,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,WAAW;QAEf,wBAAwB;QACxB,IAAI,YAAY;YACd,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,YAAY,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACjE,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAErE;QAEA,8BAA8B;QAC9B,IAAI,qBAAqB,OAAO;YAC9B,WAAW,SAAS,MAAM,CAAC,CAAA;gBACzB,IAAI,qBAAqB,YAAY;oBACnC,OAAO,CAAC,QAAQ,UAAU;gBAC5B;gBACA,OAAO,QAAQ,UAAU,EAAE,WAAW;YACxC;QACF;QAEA,oBAAoB;IACtB;IAEA,MAAM,yBAAyB,CAAC,WAAmB;QACjD,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBAC3B,IAAI,QAAQ,EAAE,KAAK,WAAW;oBAC5B,OAAO;wBACL,GAAG,OAAO;wBACV,YAAY;4BACV,GAAG,QAAQ,UAAU;4BACrB,YAAY;4BACZ,iBAAiB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;4BACtC;4BACA,WAAW;4BACX,IAAI,QAAQ,UAAU,EAAE,MAAM;4BAC9B,YAAY,QAAQ,UAAU,EAAE,cAAc;4BAC9C,YAAY,QAAQ,UAAU,EAAE,cAAc;wBAChD;oBACF;gBACF;gBACA,OAAO;YACT;IACF;IAEA,MAAM,yBAAyB,CAAC,WAAmB;QACjD,oBAAoB,CAAA;YAClB,MAAM,SAAS,IAAI,IAAI;YACvB,IAAI,SAAS;gBACX,OAAO,GAAG,CAAC;YACb,OAAO;gBACL,OAAO,MAAM,CAAC;YAChB;YACA,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS;YACX,oBAAoB,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAC5D,OAAO;YACL,oBAAoB,IAAI;QAC1B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;gBAC3B,IAAI,iBAAiB,GAAG,CAAC,QAAQ,EAAE,GAAG;oBACpC,OAAO;wBACL,GAAG,OAAO;wBACV,YAAY;4BACV,GAAG,QAAQ,UAAU;4BACrB,YAAY,QAAQ,EAAE;4BACtB,iBAAiB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;4BACtC;4BACA,WAAW;4BACX,IAAI,QAAQ,UAAU,EAAE,MAAM;4BAC9B,YAAY,QAAQ,UAAU,EAAE,cAAc;4BAC9C,YAAY,QAAQ,UAAU,EAAE,cAAc;wBAChD;oBACF;gBACF;gBACA,OAAO;YACT;QACA,oBAAoB,IAAI;IAC1B;IAEA,MAAM,iBAAiB;QACrB,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAAW,CAAC;oBACvC,GAAG,OAAO;oBACV,YAAY;wBACV,GAAG,QAAQ,UAAU;wBACrB,YAAY,QAAQ,EAAE;wBACtB,iBAAiB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;wBACtC,QAAQ;wBACR,WAAW;wBACX,IAAI,QAAQ,UAAU,EAAE,MAAM;wBAC9B,YAAY,QAAQ,UAAU,EAAE,cAAc;wBAC9C,YAAY,QAAQ,UAAU,EAAE,cAAc;oBAChD;gBACF,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB,YAAY,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,UAAW,CAAC;oBACvC,GAAG,OAAO;oBACV,YAAY;gBACd,CAAC;IACH;IAEA,MAAM,iBAAiB;QACrB,UAAU;QACV,IAAI;YACF,MAAM,iBAAiB,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;oBAC9C,YAAY,QAAQ,EAAE;oBACtB,iBAAiB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;oBACtC,QAAQ,QAAQ,UAAU,EAAE,UAAU;oBACtC,WAAW;gBACb,CAAC;YAED,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAe;YACxC;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;YAElC,MAAM;YACN,MAAM,6BAA6B,6BAA6B;;QAClE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,+BAA+B;QACnC,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,MAAM;YACN;QACF;QAEA,mBAAmB;QACnB,IAAI;YACF,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,UAAU,EAAE,WAAW;YAEjF,IAAI,eAAe,MAAM,KAAK,GAAG;gBAC/B,MAAM;gBACN;YACF;YAEA,MAAM,WAAW,EAAE;YACnB,KAAK,MAAM,WAAW,eAAgB;gBACpC,8BAA8B;gBAC9B,IAAI,QAAQ,aAAa,EAAE;oBACzB,SAAS,IAAI,CAAC;wBACZ,YAAY,QAAQ,EAAE;wBACtB,iBAAiB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;wBACtC,iBAAiB;wBACjB,gBAAgB;wBAChB,kBAAkB,QAAQ,aAAa;oBACzC;gBACF;gBAEA,wDAAwD;gBACxD,IAAI,QAAQ,aAAa,IAAI,QAAQ,aAAa,KAAK,QAAQ,aAAa,EAAE;oBAC5E,SAAS,IAAI,CAAC;wBACZ,YAAY,QAAQ,EAAE;wBACtB,iBAAiB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;wBACtC,iBAAiB;wBACjB,gBAAgB;wBAChB,kBAAkB,QAAQ,aAAa;oBACzC;gBACF;YACF;YAEA,kEAAkE;YAClE,KAAK,MAAM,WAAW,SAAU;gBAC9B,MAAM,MAAM,4BAA4B;oBACtC,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;YACF;YAEA,MAAM,CAAC,oBAAoB,EAAE,eAAe,MAAM,CAAC,gBAAgB,CAAC;YACpE,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,WAAW,WAAW,MAAM;IACpF,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,WAAW,UAAU,MAAM;IAClF,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,UAAU,EAAE,QAAQ,MAAM;IAExE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,0JAAA,CAAA,UAAU;oDACT,UAAU;oDACV,UAAU,CAAC,OAAsB,QAAQ,gBAAgB;oDACzD,YAAW;oDACX,WAAU;oDACV,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;0CAOrB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDAChD,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;oDACnB,QAAQ,GAAG,CAAC,CAAC,oBACZ,8OAAC;4DAAoB,OAAO,IAAI,EAAE;;gEAC/B,IAAI,IAAI;gEAAC;gEAAI,IAAI,OAAO;;2DADd,IAAI,EAAE;;;;;;;;;;;;;;;;;kDAQzB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;;kEAEV,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;kDAK7B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAChE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS;wDACT,WAAU;wDACV,UAAU;kEAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;kEAEvB,8OAAC;wDACC,SAAS;wDACT,WAAU;wDACV,UAAU;kEAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAShC,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAmD,MAAM,aAAa;;;;;;;;;;;;;;;;;;;;;;;sCAKzF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAKtE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAKtE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAA+C;;;;;;0DAC5D,8OAAC;gDAAE,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO1E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,IAAI,GAAG,IACrB,GAAG,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,GAC5C,gBAAgB,IACd,GAAG,cAAc,wBAAwB,CAAC,GAC1C;;;;;;;;;;;;0CAKV,8OAAC;gCAAI,WAAU;;oCACZ,iBAAiB,IAAI,GAAG,mBACvB;;0DACE,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,SAAS,IAAM,gBAAgB;gDAC/B,WAAU;0DACX;;;;;;;;kDAML,8OAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,SAAS,cAAc;;;;;;;;;;;;;;;;;;;;;;;;8BAOhC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;;oDACV,iBAAiB,MAAM;oDAAC;oDAAK,SAAS,MAAM;oDAAC;oDAC7C,kBAAkB,SAAS,CAAC,kBAAkB,CAAC;;;;;;;;;;;;;oCAInD,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,SAAS,iBAAiB,IAAI,KAAK,iBAAiB,MAAM,IAAI,iBAAiB,MAAM,GAAG;gDACxF,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;gDACjD,WAAU;;;;;;0DAEZ,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAMhD,8OAAC;4BAAI,WAAU;sCACZ,wBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;uCAElC,iBAAiB,MAAM,KAAK,kBAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAE,WAAU;kDACV,cAAc,qBAAqB,QAChC,mCACA;;;;;;;;;;;qDAKR,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,8OAAC;wCAEC,WAAW,CAAC,qDAAqD,EAC/D,QAAQ,UAAU,EAAE,WAAW,YAC3B,iCACA,QAAQ,UAAU,EAAE,WAAW,WAC/B,6BACA,6CACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,SAAS,iBAAiB,GAAG,CAAC,QAAQ,EAAE;gEACxC,UAAU,CAAC,IAAM,uBAAuB,QAAQ,EAAE,EAAE,EAAE,MAAM,CAAC,OAAO;gEACpE,WAAU;;;;;;0EAEZ,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAkD,QAAQ,YAAY;;;;;;kFACpF,8OAAC;wEAAE,WAAU;;4EAAmC;4EACtC,QAAQ,KAAK,EAAE,OAAO,GAAG,QAAQ,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,KAAK,CAAC,OAAO,EAAE,GAAG,QAAQ,QAAQ;;;;;;;kFAErG,8OAAC;wEAAE,WAAU;;4EAAwB;4EAAS,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;kEAGrE,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,UAAU,EAAE,WAAW,2BAC9B,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAExB,QAAQ,UAAU,EAAE,WAAW,0BAC9B,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;;;0DAKzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,uBAAuB,QAAQ,EAAE,EAAE;wDAClD,WAAW,CAAC,6EAA6E,EACvF,QAAQ,UAAU,EAAE,WAAW,YAC3B,4BACA,qEACJ;;0EAEF,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,8OAAC;wDACC,SAAS,IAAM,uBAAuB,QAAQ,EAAE,EAAE;wDAClD,WAAW,CAAC,6EAA6E,EACvF,QAAQ,UAAU,EAAE,WAAW,WAC3B,0BACA,iEACJ;;0EAEF,8OAAC,4MAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;0EACnB,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;4CAItC,QAAQ,UAAU,EAAE,WAAW,0BAC9B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAE;gEAAY,QAAQ,aAAa,IAAI;;;;;;;sEACxC,8OAAC;;gEAAE;gEAAY,QAAQ,aAAa,IAAI;;;;;;;;;;;;;;;;;;;uCAhEzC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;gBA4E1B,cAAc,mBACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;sCAGpD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAA+C;;;;;;sDAGlF,8OAAC;4CACC,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,MAAM;4CACN,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;gDAAwB;gDACrB;gDAAY;;;;;;;sDAE5B,8OAAC;4CACC,SAAS;4CACT,UAAU,mBAAmB,CAAC,YAAY,IAAI;4CAC9C,WAAU;;8DAEV,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,kBAAkB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpD", "debugId": null}}, {"offset": {"line": 4887, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/components/DashboardAnalytics.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { format } from 'date-fns'\nimport { \n  Users, \n  CheckCircle, \n  XCircle, \n  TrendingUp, \n  DollarSign, \n  Calendar,\n  BarChart3,\n  Pie<PERSON>hart\n} from 'lucide-react'\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  Pie<PERSON>hart as RechartsPieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer\n} from 'recharts'\n\ninterface AttendanceStats {\n  totalStudents: number\n  presentCount: number\n  absentCount: number\n  attendancePercentage: number\n}\n\ninterface FeeStats {\n  totalCollected: number\n  totalPending: number\n  totalStudents: number\n  studentsWithPending: number\n}\n\ninterface AttendanceTrend {\n  date: string\n  presentCount: number\n  absentCount: number\n  attendancePercentage: number\n}\n\nconst COLORS = ['#10B981', '#EF4444', '#F59E0B', '#3B82F6']\n\nexport default function DashboardAnalytics() {\n  const [attendanceStats, setAttendanceStats] = useState<AttendanceStats | null>(null)\n  const [feeStats, setFeeStats] = useState<FeeStats | null>(null)\n  const [attendanceTrends, setAttendanceTrends] = useState<AttendanceTrend[]>([])\n  const [loading, setLoading] = useState(true)\n  const [selectedDate, setSelectedDate] = useState(new Date())\n\n  useEffect(() => {\n    loadDashboardData()\n  }, [selectedDate])\n\n  const loadDashboardData = async () => {\n    setLoading(true)\n    try {\n      const dateStr = format(selectedDate, 'yyyy-MM-dd')\n      \n      // Load attendance statistics for selected date\n      const attendanceResponse = await fetch(`/api/attendance/statistics?date=${dateStr}`)\n      if (attendanceResponse.ok) {\n        const attendanceData = await attendanceResponse.json()\n        setAttendanceStats(attendanceData)\n      }\n\n      // Load fee statistics\n      const feeResponse = await fetch('/api/pending-fees')\n      if (feeResponse.ok) {\n        const feeData = await feeResponse.json()\n        // Calculate fee statistics from the response\n        const totalCollected = feeData.reduce((sum: number, student: any) => sum + (student.totalPaid || 0), 0)\n        const totalPending = feeData.reduce((sum: number, student: any) => sum + (student.totalPending || 0), 0)\n        \n        setFeeStats({\n          totalCollected,\n          totalPending,\n          totalStudents: feeData.length,\n          studentsWithPending: feeData.filter((student: any) => student.totalPending > 0).length\n        })\n      }\n\n      // Load attendance trends for last 30 days\n      const trendsResponse = await fetch('/api/attendance/trends?days=30')\n      if (trendsResponse.ok) {\n        const trendsData = await trendsResponse.json()\n        setAttendanceTrends(trendsData)\n      }\n    } catch (error) {\n      console.error('Error loading dashboard data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const attendancePieData = attendanceStats ? [\n    { name: 'Present', value: attendanceStats.presentCount, color: '#10B981' },\n    { name: 'Absent', value: attendanceStats.absentCount, color: '#EF4444' }\n  ] : []\n\n  const feesPieData = feeStats ? [\n    { name: 'Collected', value: feeStats.totalCollected, color: '#10B981' },\n    { name: 'Pending', value: feeStats.totalPending, color: '#F59E0B' }\n  ] : []\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-96\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              First Step School Dashboard\n            </h2>\n            <p className=\"text-gray-600\">Saurabh Vihar, Jaitpur, Delhi</p>\n          </div>\n          \n          <div className=\"flex items-center gap-2\">\n            <Calendar className=\"w-5 h-5 text-gray-500\" />\n            <input\n              type=\"date\"\n              value={format(selectedDate, 'yyyy-MM-dd')}\n              onChange={(e) => setSelectedDate(new Date(e.target.value))}\n              className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              max={format(new Date(), 'yyyy-MM-dd')}\n            />\n          </div>\n        </div>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {/* Total Students */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <Users className=\"w-6 h-6 text-blue-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Total Students</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {attendanceStats?.totalStudents || 0}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Present Today */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-green-100 rounded-lg\">\n              <CheckCircle className=\"w-6 h-6 text-green-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Present Today</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {attendanceStats?.presentCount || 0}\n              </p>\n              <p className=\"text-xs text-green-600\">\n                {attendanceStats?.attendancePercentage.toFixed(1)}% attendance\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Absent Today */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-red-100 rounded-lg\">\n              <XCircle className=\"w-6 h-6 text-red-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Absent Today</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                {attendanceStats?.absentCount || 0}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Fee Collection */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div className=\"flex items-center\">\n            <div className=\"p-2 bg-yellow-100 rounded-lg\">\n              <DollarSign className=\"w-6 h-6 text-yellow-600\" />\n            </div>\n            <div className=\"ml-4\">\n              <p className=\"text-sm font-medium text-gray-500\">Fees Collected</p>\n              <p className=\"text-2xl font-bold text-gray-900\">\n                ₹{feeStats?.totalCollected.toLocaleString() || 0}\n              </p>\n              <p className=\"text-xs text-yellow-600\">\n                ₹{feeStats?.totalPending.toLocaleString() || 0} pending\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Charts Row */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Attendance Trend Chart */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div className=\"flex items-center gap-2 mb-4\">\n            <TrendingUp className=\"w-5 h-5 text-blue-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900\">Attendance Trends (30 Days)</h3>\n          </div>\n          \n          <ResponsiveContainer width=\"100%\" height={300}>\n            <LineChart data={attendanceTrends}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis \n                dataKey=\"date\" \n                tickFormatter={(value) => format(new Date(value), 'MM/dd')}\n              />\n              <YAxis />\n              <Tooltip \n                labelFormatter={(value) => format(new Date(value), 'MMM dd, yyyy')}\n              />\n              <Legend />\n              <Line \n                type=\"monotone\" \n                dataKey=\"presentCount\" \n                stroke=\"#10B981\" \n                strokeWidth={2}\n                name=\"Present\"\n              />\n              <Line \n                type=\"monotone\" \n                dataKey=\"absentCount\" \n                stroke=\"#EF4444\" \n                strokeWidth={2}\n                name=\"Absent\"\n              />\n            </LineChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Today's Attendance Pie Chart */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div className=\"flex items-center gap-2 mb-4\">\n            <PieChart className=\"w-5 h-5 text-green-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900\">Today's Attendance</h3>\n          </div>\n          \n          <ResponsiveContainer width=\"100%\" height={300}>\n            <RechartsPieChart>\n              <Pie\n                data={attendancePieData}\n                cx=\"50%\"\n                cy=\"50%\"\n                innerRadius={60}\n                outerRadius={100}\n                paddingAngle={5}\n                dataKey=\"value\"\n              >\n                {attendancePieData.map((entry, index) => (\n                  <Cell key={`cell-${index}`} fill={entry.color} />\n                ))}\n              </Pie>\n              <Tooltip />\n              <Legend />\n            </RechartsPieChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Fee Collection Charts */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Fee Collection Overview */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div className=\"flex items-center gap-2 mb-4\">\n            <BarChart3 className=\"w-5 h-5 text-yellow-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900\">Fee Collection Overview</h3>\n          </div>\n\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <RechartsPieChart>\n              <Pie\n                data={feesPieData}\n                cx=\"50%\"\n                cy=\"50%\"\n                innerRadius={60}\n                outerRadius={100}\n                paddingAngle={5}\n                dataKey=\"value\"\n              >\n                {feesPieData.map((entry, index) => (\n                  <Cell key={`cell-${index}`} fill={entry.color} />\n                ))}\n              </Pie>\n              <Tooltip formatter={(value) => `₹${value.toLocaleString()}`} />\n              <Legend />\n            </RechartsPieChart>\n          </ResponsiveContainer>\n        </div>\n\n        {/* Attendance Percentage Trend */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <div className=\"flex items-center gap-2 mb-4\">\n            <TrendingUp className=\"w-5 h-5 text-purple-500\" />\n            <h3 className=\"text-lg font-semibold text-gray-900\">Attendance Percentage Trend</h3>\n          </div>\n\n          <ResponsiveContainer width=\"100%\" height={300}>\n            <AreaChart data={attendanceTrends}>\n              <CartesianGrid strokeDasharray=\"3 3\" />\n              <XAxis\n                dataKey=\"date\"\n                tickFormatter={(value) => format(new Date(value), 'MM/dd')}\n              />\n              <YAxis domain={[0, 100]} />\n              <Tooltip\n                labelFormatter={(value) => format(new Date(value), 'MMM dd, yyyy')}\n                formatter={(value) => [`${value}%`, 'Attendance']}\n              />\n              <Area\n                type=\"monotone\"\n                dataKey=\"attendancePercentage\"\n                stroke=\"#8B5CF6\"\n                fill=\"#8B5CF6\"\n                fillOpacity={0.3}\n              />\n            </AreaChart>\n          </ResponsiveContainer>\n        </div>\n      </div>\n\n      {/* Summary Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Attendance Summary */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Attendance Summary</h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Total Students</span>\n              <span className=\"font-semibold\">{attendanceStats?.totalStudents || 0}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Present Today</span>\n              <span className=\"font-semibold text-green-600\">{attendanceStats?.presentCount || 0}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Absent Today</span>\n              <span className=\"font-semibold text-red-600\">{attendanceStats?.absentCount || 0}</span>\n            </div>\n            <div className=\"flex justify-between items-center pt-2 border-t\">\n              <span className=\"text-gray-600\">Attendance Rate</span>\n              <span className=\"font-semibold text-blue-600\">\n                {attendanceStats?.attendancePercentage.toFixed(1)}%\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Fee Summary */}\n        <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Fee Collection Summary</h3>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Total Students</span>\n              <span className=\"font-semibold\">{feeStats?.totalStudents || 0}</span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Fees Collected</span>\n              <span className=\"font-semibold text-green-600\">\n                ₹{feeStats?.totalCollected.toLocaleString() || 0}\n              </span>\n            </div>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-gray-600\">Pending Fees</span>\n              <span className=\"font-semibold text-yellow-600\">\n                ₹{feeStats?.totalPending.toLocaleString() || 0}\n              </span>\n            </div>\n            <div className=\"flex justify-between items-center pt-2 border-t\">\n              <span className=\"text-gray-600\">Students with Pending</span>\n              <span className=\"font-semibold text-red-600\">\n                {feeStats?.studentsWithPending || 0}\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Quick Actions</h3>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n            <Users className=\"w-6 h-6 text-blue-500 mb-2\" />\n            <p className=\"font-medium text-gray-900\">Mark Attendance</p>\n            <p className=\"text-sm text-gray-600\">Record today's attendance</p>\n          </button>\n\n          <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n            <DollarSign className=\"w-6 h-6 text-green-500 mb-2\" />\n            <p className=\"font-medium text-gray-900\">Collect Fees</p>\n            <p className=\"text-sm text-gray-600\">Record fee payments</p>\n          </button>\n\n          <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n            <BarChart3 className=\"w-6 h-6 text-purple-500 mb-2\" />\n            <p className=\"font-medium text-gray-900\">View Reports</p>\n            <p className=\"text-sm text-gray-600\">Generate detailed reports</p>\n          </button>\n\n          <button className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left\">\n            <Calendar className=\"w-6 h-6 text-orange-500 mb-2\" />\n            <p className=\"font-medium text-gray-900\">View Calendar</p>\n            <p className=\"text-sm text-gray-600\">Check school calendar</p>\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAdA;;;;;;AAqDA,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;CAAU;AAE5C,SAAS;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAC9E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAErD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,oBAAoB;QACxB,WAAW;QACX,IAAI;YACF,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;YAErC,+CAA+C;YAC/C,MAAM,qBAAqB,MAAM,MAAM,CAAC,gCAAgC,EAAE,SAAS;YACnF,IAAI,mBAAmB,EAAE,EAAE;gBACzB,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;gBACpD,mBAAmB;YACrB;YAEA,sBAAsB;YACtB,MAAM,cAAc,MAAM,MAAM;YAChC,IAAI,YAAY,EAAE,EAAE;gBAClB,MAAM,UAAU,MAAM,YAAY,IAAI;gBACtC,6CAA6C;gBAC7C,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAC,KAAa,UAAiB,MAAM,CAAC,QAAQ,SAAS,IAAI,CAAC,GAAG;gBACrG,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAC,KAAa,UAAiB,MAAM,CAAC,QAAQ,YAAY,IAAI,CAAC,GAAG;gBAEtG,YAAY;oBACV;oBACA;oBACA,eAAe,QAAQ,MAAM;oBAC7B,qBAAqB,QAAQ,MAAM,CAAC,CAAC,UAAiB,QAAQ,YAAY,GAAG,GAAG,MAAM;gBACxF;YACF;YAEA,0CAA0C;YAC1C,MAAM,iBAAiB,MAAM,MAAM;YACnC,IAAI,eAAe,EAAE,EAAE;gBACrB,MAAM,aAAa,MAAM,eAAe,IAAI;gBAC5C,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,kBAAkB;QAC1C;YAAE,MAAM;YAAW,OAAO,gBAAgB,YAAY;YAAE,OAAO;QAAU;QACzE;YAAE,MAAM;YAAU,OAAO,gBAAgB,WAAW;YAAE,OAAO;QAAU;KACxE,GAAG,EAAE;IAEN,MAAM,cAAc,WAAW;QAC7B;YAAE,MAAM;YAAa,OAAO,SAAS,cAAc;YAAE,OAAO;QAAU;QACtE;YAAE,MAAM;YAAW,OAAO,SAAS,YAAY;YAAE,OAAO;QAAU;KACnE,GAAG,EAAE;IAEN,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCACC,MAAK;oCACL,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;oCAC5B,UAAU,CAAC,IAAM,gBAAgB,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK;oCACxD,WAAU;oCACV,KAAK,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0BAOhC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,iBAAiB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;kCAO3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,iBAAiB,gBAAgB;;;;;;sDAEpC,8OAAC;4CAAE,WAAU;;gDACV,iBAAiB,qBAAqB,QAAQ;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDACV,iBAAiB,eAAe;;;;;;;;;;;;;;;;;;;;;;;kCAOzC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;;gDAAmC;gDAC5C,UAAU,eAAe,oBAAoB;;;;;;;sDAEjD,8OAAC;4CAAE,WAAU;;gDAA0B;gDACnC,UAAU,aAAa,oBAAoB;gDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,eAAe,CAAC,QAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ;;;;;;sDAEpD,8OAAC,qJAAA,CAAA,QAAK;;;;;sDACN,8OAAC,uJAAA,CAAA,UAAO;4CACN,gBAAgB,CAAC,QAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ;;;;;;sDAErD,8OAAC,sJAAA,CAAA,SAAM;;;;;sDACP,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;4CACb,MAAK;;;;;;sDAEP,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,aAAa;4CACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;kCAOb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,oJAAA,CAAA,WAAgB;;sDACf,8OAAC,+IAAA,CAAA,MAAG;4CACF,MAAM;4CACN,IAAG;4CACH,IAAG;4CACH,aAAa;4CACb,aAAa;4CACb,cAAc;4CACd,SAAQ;sDAEP,kBAAkB,GAAG,CAAC,CAAC,OAAO,sBAC7B,8OAAC,oJAAA,CAAA,OAAI;oDAAuB,MAAM,MAAM,KAAK;mDAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;sDAG9B,8OAAC,uJAAA,CAAA,UAAO;;;;;sDACR,8OAAC,sJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,oJAAA,CAAA,WAAgB;;sDACf,8OAAC,+IAAA,CAAA,MAAG;4CACF,MAAM;4CACN,IAAG;4CACH,IAAG;4CACH,aAAa;4CACb,aAAa;4CACb,cAAc;4CACd,SAAQ;sDAEP,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC,oJAAA,CAAA,OAAI;oDAAuB,MAAM,MAAM,KAAK;mDAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;sDAG9B,8OAAC,uJAAA,CAAA,UAAO;4CAAC,WAAW,CAAC,QAAU,CAAC,CAAC,EAAE,MAAM,cAAc,IAAI;;;;;;sDAC3D,8OAAC,sJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;kCAMb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAGtD,8OAAC,mKAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAO,QAAQ;0CACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oCAAC,MAAM;;sDACf,8OAAC,6JAAA,CAAA,gBAAa;4CAAC,iBAAgB;;;;;;sDAC/B,8OAAC,qJAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,eAAe,CAAC,QAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ;;;;;;sDAEpD,8OAAC,qJAAA,CAAA,QAAK;4CAAC,QAAQ;gDAAC;gDAAG;6CAAI;;;;;;sDACvB,8OAAC,uJAAA,CAAA,UAAO;4CACN,gBAAgB,CAAC,QAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ;4CACnD,WAAW,CAAC,QAAU;oDAAC,GAAG,MAAM,CAAC,CAAC;oDAAE;iDAAa;;;;;;sDAEnD,8OAAC,oJAAA,CAAA,OAAI;4CACH,MAAK;4CACL,SAAQ;4CACR,QAAO;4CACP,MAAK;4CACL,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAiB,iBAAiB,iBAAiB;;;;;;;;;;;;kDAErE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAgC,iBAAiB,gBAAgB;;;;;;;;;;;;kDAEnF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAA8B,iBAAiB,eAAe;;;;;;;;;;;;kDAEhF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;;oDACb,iBAAiB,qBAAqB,QAAQ;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DAAiB,UAAU,iBAAiB;;;;;;;;;;;;kDAE9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;;oDAA+B;oDAC3C,UAAU,eAAe,oBAAoB;;;;;;;;;;;;;kDAGnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;;oDAAgC;oDAC5C,UAAU,aAAa,oBAAoB;;;;;;;;;;;;;kDAGjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,8OAAC;gDAAK,WAAU;0DACb,UAAU,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD", "debugId": null}}, {"offset": {"line": 6154, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { CreditCard, FileText, Clock, Users, BarChart3, UserCheck } from 'lucide-react'\nimport FeeManagementForm from './FeeManagementForm'\nimport FeeRecordsComponent from './FeeRecordsComponent'\nimport PendingFeesComponent from './PendingFeesComponent'\nimport AttendanceManagement from './AttendanceManagement'\nimport DashboardAnalytics from './DashboardAnalytics'\n\ntype TabType = 'dashboard' | 'attendance' | 'collection' | 'records' | 'pending'\n\nexport default function FeeManagementDashboard() {\n  const [activeTab, setActiveTab] = useState<TabType>('dashboard')\n\n  const tabs = [\n    {\n      id: 'dashboard' as TabType,\n      name: 'Dashboard',\n      icon: BarChart3,\n      description: 'Overview of attendance and fees'\n    },\n    {\n      id: 'attendance' as TabType,\n      name: 'Attendance',\n      icon: UserCheck,\n      description: 'Mark and manage student attendance'\n    },\n    {\n      id: 'collection' as TabType,\n      name: 'Fee Collection',\n      icon: CreditCard,\n      description: 'Record new fee payments'\n    },\n    {\n      id: 'records' as TabType,\n      name: 'Fee Records',\n      icon: FileText,\n      description: 'View all fee submission records'\n    },\n    {\n      id: 'pending' as TabType,\n      name: 'Pending Fees',\n      icon: Clock,\n      description: 'View students with pending fees'\n    }\n  ]\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'dashboard':\n        return <DashboardAnalytics />\n      case 'attendance':\n        return <AttendanceManagement />\n      case 'collection':\n        return <FeeManagementForm />\n      case 'records':\n        return <FeeRecordsComponent />\n      case 'pending':\n        return <PendingFeesComponent />\n      default:\n        return <DashboardAnalytics />\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"py-6\">\n            <div className=\"text-center\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                First Step School\n              </h1>\n              <p className=\"text-lg text-gray-600\">\n                Saurabh Vihar, Jaitpur, Delhi\n              </p>\n              <p className=\"text-sm text-gray-500\">\n                School Management System\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white shadow-sm\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <nav className=\"flex space-x-8\" aria-label=\"Tabs\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon\n              const isActive = activeTab === tab.id\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`${\n                    isActive\n                      ? 'border-blue-500 text-blue-600 bg-blue-50'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  } whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors duration-200 rounded-t-lg`}\n                  aria-current={isActive ? 'page' : undefined}\n                >\n                  <Icon className=\"w-5 h-5\" />\n                  <span className=\"hidden sm:inline\">{tab.name}</span>\n                  <span className=\"sm:hidden\">{tab.name.split(' ')[0]}</span>\n                </button>\n              )\n            })}\n          </nav>\n        </div>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"bg-white rounded-lg shadow-sm border\">\n          <div className=\"p-6\">\n            {/* Tab Description */}\n            <div className=\"mb-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">\n                {tabs.find(tab => tab.id === activeTab)?.name}\n              </h2>\n              <p className=\"text-gray-600\">\n                {tabs.find(tab => tab.id === activeTab)?.description}\n              </p>\n            </div>\n\n            {/* Tab Content */}\n            {renderTabContent()}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAYe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEpD,MAAM,OAAO;QACX;YACE,IAAI;YACJ,MAAM;YACN,MAAM,kNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;YACf,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;YAChB,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,IAAI;Y<PERSON><PERSON>,MAAM;YAC<PERSON>,MAAM,oMAAA,CAAA,QAAK;YACX,aAAa;QACf;KACD;IAED,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,wIAAA,CAAA,UAAkB;;;;;YAC5B,KAAK;gBACH,qBAAO,8OAAC,0IAAA,CAAA,UAAoB;;;;;YAC9B,KAAK;gBACH,qBAAO,8OAAC,uIAAA,CAAA,UAAiB;;;;;YAC3B,KAAK;gBACH,qBAAO,8OAAC,yIAAA,CAAA,UAAmB;;;;;YAC7B,KAAK;gBACH,qBAAO,8OAAC,0IAAA,CAAA,UAAoB;;;;;YAC9B;gBACE,qBAAO,8OAAC,wIAAA,CAAA,UAAkB;;;;;QAC9B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAiB,cAAW;kCACxC,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,OAAO,IAAI,IAAI;4BACrB,MAAM,WAAW,cAAc,IAAI,EAAE;4BACrC,qBACE,8OAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,GACT,WACI,6CACA,6EACL,+HAA+H,CAAC;gCACjI,gBAAc,WAAW,SAAS;;kDAElC,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAoB,IAAI,IAAI;;;;;;kDAC5C,8OAAC;wCAAK,WAAU;kDAAa,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;+BAX9C,IAAI,EAAE;;;;;wBAcjB;;;;;;;;;;;;;;;;0BAMN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;kDAE3C,8OAAC;wCAAE,WAAU;kDACV,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;;;;;;;4BAK5C;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}