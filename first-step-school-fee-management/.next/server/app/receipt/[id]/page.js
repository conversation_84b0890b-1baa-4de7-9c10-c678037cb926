(()=>{var e={};e.id=685,e.ids=[685],e.modules={163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return s}});let s=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},263:(e,t,r)=>{Promise.resolve().then(r.bind(r,14344))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,r)=>{"use strict";r.d(t,{FN:()=>u,Nz:()=>a,Pe:()=>c,Rc:()=>n,Wc:()=>f,hJ:()=>l,my:()=>m,nh:()=>i,pQ:()=>d,q9:()=>p,rq:()=>o});var s=r(56621);async function n(e){let{data:t,error:r}=await s.N.schema("school").from("fee_payments").select(`
      *,
      student:IDCard(
        *,
        class:Class(name, section)
      )
    `).eq("receipt_url",e).single();if(r){if("PGRST116"===r.code)return null;throw r}return t}async function a(){let e=new Date().getMonth()+1,t=new Date().getFullYear(),{data:r,error:n}=await s.N.schema("school").from("IDCard").select(`
      *,
      class:Class(name, section)
    `);if(n)throw n;let{data:a,error:i}=await s.N.schema("school").from("fee_payments").select("*");if(i)throw i;let o=new Set((a||[]).filter(r=>{let s=new Date(r.payment_date),n=s.getMonth()+1,a=s.getFullYear();return n===e&&a===t&&"completed"===r.payment_status&&0===r.balance_remaining}).map(e=>e.student_id)),l=new Map;return a?.forEach(e=>{l.has(e.student_id)||l.set(e.student_id,[]),l.get(e.student_id).push(e)}),(r||[]).filter(e=>!o.has(e.id)).map(r=>{let s=l.get(r.id)||[],n=s.reduce((e,t)=>e+(parseFloat(t.amount_received)||0),0),a=s.reduce((e,t)=>e+(parseFloat(t.balance_remaining)||0),0),i=s.sort((e,t)=>new Date(t.payment_date).getTime()-new Date(e.payment_date).getTime())[0],o=s.find(r=>{let s=new Date(r.payment_date);return s.getMonth()+1===e&&s.getFullYear()===t}),d=a;return o||(d+=1e3),{...r,totalPaid:n,totalPending:d,lastPaymentDate:i?.payment_date,lastPaymentAmount:i?parseFloat(i.amount_received):null,pendingReason:o?`Outstanding balance: ₹${a}`:`No payment for ${e}/${t}`}})}async function i(){let{data:e,error:t}=await s.N.schema("school").from("fee_payments").select("amount_received, balance_remaining");if(t)throw t;let{data:r,error:n}=await s.N.schema("school").from("IDCard").select("id");if(n)throw n;let i=e?.reduce((e,t)=>e+t.amount_received,0)||0,o=e?.reduce((e,t)=>e+t.balance_remaining,0)||0,l=await a();return{totalCollected:i,totalPending:o,totalStudents:r?.length||0,studentsWithPending:l.length}}async function o(e){{let{data:t,error:r}=await s.N.schema("school").from("attendance").upsert(e,{onConflict:"student_id,attendance_date",ignoreDuplicates:!1}).select("*").single();if(r)throw r;return t}}async function l(e){{let{data:t,error:r}=await s.N.schema("school").from("attendance").upsert(e,{onConflict:"student_id,attendance_date",ignoreDuplicates:!1}).select("*");if(r)throw r;return t||[]}}async function d(e){{let{data:t,error:r}=await s.N.schema("school").from("attendance").select(`
        *,
        student:IDCard(*)
      `).eq("attendance_date",e).order("created_at",{ascending:!1});if(r)throw r;return t||[]}}async function c(e,t){{let r=s.N.schema("school").from("IDCard").select(`
        *,
        class:Class(name, section)
      `).order("student_name");t&&"all"!==t&&(r=r.eq("class_id",t));let{data:n,error:a}=await r;if(a)throw a;let{data:i,error:o}=await s.N.schema("school").from("attendance").select("*").eq("attendance_date",e);if(o)throw o;return n?.map(e=>{let t=i?.find(t=>t.student_id===e.id);return{...e,attendance:t}})||[]}}async function u(e,t){{let r=s.N.schema("school").from("IDCard").select("id",{count:"exact"});t&&"all"!==t&&(r=r.eq("class_id",t));let{data:n,error:a}=await r;if(a)throw a;let i=s.N.schema("school").from("attendance").select(`
        status,
        student:IDCard!inner(class_id)
      `).eq("attendance_date",e);t&&"all"!==t&&(i=i.eq("student.class_id",t));let{data:o,error:l}=await i;if(l)throw l;let d=n?.length||0,c=o?.filter(e=>"present"===e.status).length||0;return{totalStudents:d,presentCount:c,absentCount:o?.filter(e=>"absent"===e.status).length||0,attendancePercentage:Math.round(100*(d>0?c/d*100:0))/100}}}async function m(e=30){{let t=new Date,r=new Date;r.setDate(t.getDate()-e);let{data:n,error:a}=await s.N.schema("school").from("attendance").select("attendance_date, status").gte("attendance_date",r.toISOString().split("T")[0]).lte("attendance_date",t.toISOString().split("T")[0]).order("attendance_date");if(a)throw a;let{data:i,error:o}=await s.N.schema("school").from("IDCard").select("id",{count:"exact"});if(o)throw o;let l=i?.length||0;return Object.entries(n?.reduce((e,t)=>{let r=t.attendance_date;return e[r]||(e[r]={present:0,absent:0}),"present"===t.status?e[r].present++:e[r].absent++,e},{})||{}).map(([e,t])=>({date:e,presentCount:t.present,absentCount:t.absent,attendancePercentage:l>0?Math.round(t.present/l*1e4)/100:0}))}}async function p(e){{let{data:t,error:r}=await s.N.schema("school").from("attendance_messages").insert(e).select("*").single();if(r)throw r;return t}}async function f(e){{let t=s.N.schema("school").from("attendance_messages").select(`
        *,
        student:IDCard(*)
      `).order("created_at",{ascending:!1});e&&(t=t.eq("attendance_date",e));let{data:r,error:n}=await t;if(n)throw n;return r||[]}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14344:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var s=r(60687),n=r(58709);let a=(0,r(62688).A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]);var i=r(81620);function o({payment:e}){return(0,s.jsxs)("div",{className:"bg-white min-h-screen lg:min-h-0",children:[(0,s.jsxs)("div",{className:"print:hidden mb-4 lg:mb-6 flex flex-col sm:flex-row justify-end gap-3",children:[(0,s.jsxs)("button",{onClick:()=>{window.print()},className:"flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm lg:text-base",children:[(0,s.jsx)(a,{className:"w-4 h-4"}),"Print Receipt"]}),(0,s.jsxs)("button",{onClick:()=>{navigator.share?navigator.share({title:`Fee Receipt - ${e.student.student_name}`,text:`Fee payment receipt for ${e.student.student_name}`,url:window.location.href}):(navigator.clipboard.writeText(window.location.href),alert("Receipt URL copied to clipboard!"))},className:"flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm lg:text-base",children:[(0,s.jsx)(i.A,{className:"w-4 h-4"}),"Share"]})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 lg:p-8 print:shadow-none print:border-none",children:[(0,s.jsxs)("div",{className:"text-center border-b border-gray-200 pb-4 lg:pb-6 mb-4 lg:mb-6",children:[(0,s.jsx)("h1",{className:"text-xl lg:text-3xl font-bold text-gray-900 mb-2",children:"First Step School"}),(0,s.jsx)("p",{className:"text-sm lg:text-base text-gray-600 mb-1",children:"Your School Address Here"}),(0,s.jsxs)("p",{className:"text-sm lg:text-base text-gray-600 mb-4",children:["Website: ","www.firststepschool.com"]}),(0,s.jsx)("h2",{className:"text-lg lg:text-xl font-semibold text-gray-800",children:"FEE PAYMENT RECEIPT"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 mb-4 lg:mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-3",children:"Receipt Information"}),(0,s.jsxs)("div",{className:"space-y-2 text-xs lg:text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Receipt ID:"}),(0,s.jsx)("span",{className:"font-medium",children:e.id.slice(-8).toUpperCase()})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Payment Date:"}),(0,s.jsx)("span",{className:"font-medium",children:(0,n.GP)(new Date(e.payment_date),"dd MMM yyyy")})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Generated On:"}),(0,s.jsx)("span",{className:"font-medium",children:(0,n.GP)(new Date(e.created_at),"dd MMM yyyy, hh:mm a")})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-3",children:"Payment Status"}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)("span",{className:`inline-block px-3 py-1 rounded-full text-xs lg:text-sm font-medium ${(e=>{switch(e){case"completed":return"text-green-600 bg-green-100";case"partial":return"text-yellow-600 bg-yellow-100";case"pending":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(e.payment_status)}`,children:e.payment_status.charAt(0).toUpperCase()+e.payment_status.slice(1)})})]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 lg:p-6 mb-4 lg:mb-6",children:[(0,s.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-4",children:"Student Information"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2 text-xs lg:text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Student Name:"}),(0,s.jsx)("span",{className:"font-medium break-words",children:e.student.student_name})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Class:"}),(0,s.jsx)("span",{className:"font-medium",children:e.student.class?`${e.student.class.name} - ${e.student.class.section}`:e.student.class_id})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Father's Name:"}),(0,s.jsx)("span",{className:"font-medium break-words",children:e.student.father_name})]})]}),(0,s.jsxs)("div",{className:"space-y-2 text-xs lg:text-sm",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Mother's Name:"}),(0,s.jsx)("span",{className:"font-medium break-words",children:e.student.mother_name})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Father's Mobile:"}),(0,s.jsx)("span",{className:"font-medium",children:e.student.father_mobile||"N/A"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{className:"text-gray-600",children:"Mother's Mobile:"}),(0,s.jsx)("span",{className:"font-medium",children:e.student.mother_mobile||"N/A"})]})]})]})]}),(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 lg:p-6 mb-4 lg:mb-6",children:[(0,s.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-4",children:"Payment Details"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,s.jsx)("span",{className:"text-gray-600 text-sm lg:text-base",children:"Amount Received:"}),(0,s.jsxs)("span",{className:"text-lg lg:text-2xl font-bold text-green-600",children:["₹",e.amount_received.toFixed(2)]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,s.jsx)("span",{className:"text-gray-600 text-sm lg:text-base",children:"Payment Method:"}),(0,s.jsx)("span",{className:"font-medium text-sm lg:text-base",children:(e=>{switch(e){case"bank_transfer":return"Bank Transfer";case"upi":return"UPI";default:return e.charAt(0).toUpperCase()+e.slice(1)}})(e.payment_method)})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,s.jsx)("span",{className:"text-gray-600 text-sm lg:text-base",children:"Balance Remaining:"}),(0,s.jsxs)("span",{className:`font-medium text-sm lg:text-base ${e.balance_remaining>0?"text-red-600":"text-green-600"}`,children:["₹",e.balance_remaining.toFixed(2)]})]})]})]}),e.notes&&(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-4 lg:mb-6",children:[(0,s.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-2",children:"Additional Notes"}),(0,s.jsx)("p",{className:"text-gray-700 text-sm lg:text-base break-words",children:e.notes})]}),(0,s.jsxs)("div",{className:"border-t border-gray-200 pt-4 lg:pt-6 text-center",children:[(0,s.jsx)("p",{className:"text-xs lg:text-sm text-gray-600 mb-2",children:"This is a computer-generated receipt and does not require a signature."}),(0,s.jsx)("p",{className:"text-xs lg:text-sm text-gray-600",children:"For any queries, please contact the school office."}),(0,s.jsxs)("div",{className:"mt-4 text-xs text-gray-500 break-all",children:["Receipt URL: ",""]})]})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26036:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30391:()=>{},33596:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35967:()=>{},39727:()=>{},47990:()=>{},48976:(e,t,r)=>{"use strict";function s(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return s}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53e3:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["receipt",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,64040)),"/home/<USER>/school/first-step-school-fee-management/src/app/receipt/[id]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/school/first-step-school-fee-management/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/home/<USER>/school/first-step-school-fee-management/src/app/receipt/[id]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/receipt/[id]/page",pathname:"/receipt/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),n=r(4536),a=r.n(n);function i(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-6xl font-bold text-gray-900 mb-4",children:"404"}),(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Receipt Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8 max-w-md",children:"The receipt you're looking for doesn't exist or may have been removed."}),(0,s.jsx)(a(),{href:"/",className:"inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors",children:"Go to Fee Management"})]})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(66437);let n="https://ytfzqzjuhcdgcvvqihda.supabase.co",a="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0Znpxemp1aGNkZ2N2dnFpaGRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNjM1NjMsImV4cCI6MjA1MTYzOTU2M30.rXjVX0vZwZtD83oztSpcyY6331t6aitjgsvKuTgUzfg",i=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!n)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let o=(0,s.UU)(n,a);i&&(0,s.UU)(n,i)},61135:()=>{},62765:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return n}});let s=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function n(){let e=Object.defineProperty(Error(s),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=s,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64040:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,generateMetadata:()=>l});var s=r(37413),n=r(97576),a=r(6710),i=r(66339);async function o({params:e}){let{id:t}=await e,r=`/receipt/${t}`,o=null;try{o=await (0,a.Rc)(r)}catch(e){console.error("Error fetching payment:",e)}return o&&o.student||(0,n.notFound)(),(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,s.jsx)("div",{className:"max-w-4xl mx-auto px-4",children:(0,s.jsx)(i.default,{payment:o})})})}async function l({params:e}){let{id:t}=await e,r=`/receipt/${t}`;try{let e=await (0,a.Rc)(r);if(!e||!e.student)return{title:"Receipt Not Found"};return{title:`Fee Receipt - ${e.student.student_name} | First Step School`,description:`Fee payment receipt for ${e.student.student_name} - Amount: ₹${e.amount_received}`}}catch(e){return{title:"Receipt Not Found"}}}},66339:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/home/<USER>/school/first-step-school-fee-management/src/components/ReceiptComponent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/school/first-step-school-fee-management/src/components/ReceiptComponent.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},70899:(e,t,r)=>{"use strict";function s(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return s}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,i.isNextRouterError)(t)||(0,a.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,o.isDynamicPostpone)(t)||(0,n.isPostpone)(t)||(0,s.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let s=r(68388),n=r(52637),a=r(51846),i=r(31162),o=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73300:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},74075:e=>{"use strict";e.exports=require("zlib")},75452:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},86897:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return i},getRedirectStatusCodeFromError:function(){return u},getRedirectTypeFromError:function(){return c},getURLFromRedirectError:function(){return d},permanentRedirect:function(){return l},redirect:function(){return o}});let s=r(52836),n=r(49026),a=r(19121).actionAsyncStorage;function i(e,t,r){void 0===r&&(r=s.RedirectStatusCode.TemporaryRedirect);let a=Object.defineProperty(Error(n.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a.digest=n.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",a}function o(e,t){var r;throw null!=t||(t=(null==a||null==(r=a.getStore())?void 0:r.isAction)?n.RedirectType.push:n.RedirectType.replace),i(e,t,s.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=n.RedirectType.replace),i(e,t,s.RedirectStatusCode.PermanentRedirect)}function d(e){return(0,n.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function c(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function u(e){if(!(0,n.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91645:e=>{"use strict";e.exports=require("net")},92015:(e,t,r)=>{Promise.resolve().then(r.bind(r,66339))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>i});var s=r(37413),n=r(7339),a=r.n(n);r(61135);let i={title:"First Step School - Management System",description:"School management system for First Step School, Saurabh Vihar, Jaitpur, Delhi. Manage attendance, fees, and student records."};function o({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:a().className,children:e})})}},94735:e=>{"use strict";e.exports=require("events")},97576:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return c},RedirectType:function(){return n.RedirectType},forbidden:function(){return i.forbidden},notFound:function(){return a.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect},unauthorized:function(){return o.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let s=r(86897),n=r(49026),a=r(62765),i=r(48976),o=r(70899),l=r(163);class d extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class c extends URLSearchParams{append(){throw new d}delete(){throw new d}set(){throw new d}sort(){throw new d}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,437,287,762],()=>r(53e3));module.exports=s})();