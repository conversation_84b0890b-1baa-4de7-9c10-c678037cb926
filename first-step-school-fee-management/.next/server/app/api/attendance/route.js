(()=>{var e={};e.id=519,e.ids=[519],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,a)=>{"use strict";a.d(t,{FN:()=>l,Nz:()=>s,Pe:()=>u,Rc:()=>n,Wc:()=>h,hJ:()=>c,my:()=>p,nh:()=>o,pQ:()=>d,q9:()=>m,rq:()=>i});var r=a(56621);async function n(e){let{data:t,error:a}=await r.N.schema("school").from("fee_payments").select(`
      *,
      student:IDCard(
        *,
        class:Class(name, section)
      )
    `).eq("receipt_url",e).single();if(a){if("PGRST116"===a.code)return null;throw a}return t}async function s(){let e=new Date().getMonth()+1,t=new Date().getFullYear(),{data:a,error:n}=await r.N.schema("school").from("IDCard").select(`
      *,
      class:Class(name, section)
    `);if(n)throw n;let{data:s,error:o}=await r.N.schema("school").from("fee_payments").select("*");if(o)throw o;let i=new Set((s||[]).filter(a=>{let r=new Date(a.payment_date),n=r.getMonth()+1,s=r.getFullYear();return n===e&&s===t&&"completed"===a.payment_status&&0===a.balance_remaining}).map(e=>e.student_id)),c=new Map;return s?.forEach(e=>{c.has(e.student_id)||c.set(e.student_id,[]),c.get(e.student_id).push(e)}),(a||[]).filter(e=>!i.has(e.id)).map(a=>{let r=c.get(a.id)||[],n=r.reduce((e,t)=>e+(parseFloat(t.amount_received)||0),0),s=r.reduce((e,t)=>e+(parseFloat(t.balance_remaining)||0),0),o=r.sort((e,t)=>new Date(t.payment_date).getTime()-new Date(e.payment_date).getTime())[0],i=r.find(a=>{let r=new Date(a.payment_date);return r.getMonth()+1===e&&r.getFullYear()===t}),d=s;return i||(d+=1e3),{...a,totalPaid:n,totalPending:d,lastPaymentDate:o?.payment_date,lastPaymentAmount:o?parseFloat(o.amount_received):null,pendingReason:i?`Outstanding balance: ₹${s}`:`No payment for ${e}/${t}`}})}async function o(){let{data:e,error:t}=await r.N.schema("school").from("fee_payments").select("amount_received, balance_remaining");if(t)throw t;let{data:a,error:n}=await r.N.schema("school").from("IDCard").select("id");if(n)throw n;let o=e?.reduce((e,t)=>e+t.amount_received,0)||0,i=e?.reduce((e,t)=>e+t.balance_remaining,0)||0,c=await s();return{totalCollected:o,totalPending:i,totalStudents:a?.length||0,studentsWithPending:c.length}}async function i(e){{let{data:t,error:a}=await r.N.schema("school").from("attendance").upsert(e,{onConflict:"student_id,attendance_date",ignoreDuplicates:!1}).select("*").single();if(a)throw a;return t}}async function c(e){{let{data:t,error:a}=await r.N.schema("school").from("attendance").upsert(e,{onConflict:"student_id,attendance_date",ignoreDuplicates:!1}).select("*");if(a)throw a;return t||[]}}async function d(e){{let{data:t,error:a}=await r.N.schema("school").from("attendance").select(`
        *,
        student:IDCard(*)
      `).eq("attendance_date",e).order("created_at",{ascending:!1});if(a)throw a;return t||[]}}async function u(e){{let{data:t,error:a}=await r.N.schema("school").from("IDCard").select("*").order("student_name");if(a)throw a;let{data:n,error:s}=await r.N.schema("school").from("attendance").select("*").eq("attendance_date",e);if(s)throw s;return t?.map(e=>{let t=n?.find(t=>t.student_id===e.id);return{...e,attendance:t}})||[]}}async function l(e){{let{data:t,error:a}=await r.N.schema("school").from("IDCard").select("id",{count:"exact"});if(a)throw a;let{data:n,error:s}=await r.N.schema("school").from("attendance").select("status").eq("attendance_date",e);if(s)throw s;let o=t?.length||0,i=n?.filter(e=>"present"===e.status).length||0;return{totalStudents:o,presentCount:i,absentCount:n?.filter(e=>"absent"===e.status).length||0,attendancePercentage:Math.round(100*(o>0?i/o*100:0))/100}}}async function p(e=30){{let t=new Date,a=new Date;a.setDate(t.getDate()-e);let{data:n,error:s}=await r.N.schema("school").from("attendance").select("attendance_date, status").gte("attendance_date",a.toISOString().split("T")[0]).lte("attendance_date",t.toISOString().split("T")[0]).order("attendance_date");if(s)throw s;let{data:o,error:i}=await r.N.schema("school").from("IDCard").select("id",{count:"exact"});if(i)throw i;let c=o?.length||0;return Object.entries(n?.reduce((e,t)=>{let a=t.attendance_date;return e[a]||(e[a]={present:0,absent:0}),"present"===t.status?e[a].present++:e[a].absent++,e},{})||{}).map(([e,t])=>({date:e,presentCount:t.present,absentCount:t.absent,attendancePercentage:c>0?Math.round(t.present/c*1e4)/100:0}))}}async function m(e){{let{data:t,error:a}=await r.N.schema("school").from("attendance_messages").insert(e).select("*").single();if(a)throw a;return t}}async function h(e){{let t=r.N.schema("school").from("attendance_messages").select(`
        *,
        student:IDCard(*)
      `).order("created_at",{ascending:!1});e&&(t=t.eq("attendance_date",e));let{data:a,error:n}=await t;if(n)throw n;return a||[]}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},38982:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>m});var r={};a.r(r),a.d(r,{GET:()=>u,POST:()=>d});var n=a(96559),s=a(48088),o=a(37719),i=a(32190),c=a(6710);async function d(e){try{let t=await e.json();if(!t.student_id||!t.attendance_date||!t.status)return i.NextResponse.json({error:"Missing required fields: student_id, attendance_date, status"},{status:400});if(!["present","absent"].includes(t.status))return i.NextResponse.json({error:'Status must be either "present" or "absent"'},{status:400});let a=await (0,c.rq)({student_id:t.student_id,attendance_date:t.attendance_date,status:t.status,marked_by:t.marked_by||"admin",notes:t.notes||null});return i.NextResponse.json(a)}catch(e){return console.error("Error marking attendance:",e),i.NextResponse.json({error:"Failed to mark attendance"},{status:500})}}async function u(e){try{let{searchParams:t}=new URL(e.url),a=t.get("date");if(!a)return i.NextResponse.json({error:"Date parameter is required"},{status:400});let r=await (0,c.pQ)(a);return i.NextResponse.json(r)}catch(e){return console.error("Error fetching attendance:",e),i.NextResponse.json({error:"Failed to fetch attendance"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/attendance/route",pathname:"/api/attendance",filename:"route",bundlePath:"app/api/attendance/route"},resolvedPagePath:"/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:p,workUnitAsyncStorage:m,serverHooks:h}=l;function f(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:m})}},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,a)=>{"use strict";a.d(t,{N:()=>i});var r=a(66437);let n="https://ytfzqzjuhcdgcvvqihda.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0Znpxemp1aGNkZ2N2dnFpaGRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNjM1NjMsImV4cCI6MjA1MTYzOTU2M30.rXjVX0vZwZtD83oztSpcyY6331t6aitjgsvKuTgUzfg",o=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!n)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!s)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let i=(0,r.UU)(n,s);o&&(0,r.UU)(n,o)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,437,580],()=>a(38982));module.exports=r})();