(()=>{var e={};e.id=43,e.ids=[43],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,a)=>{"use strict";a.d(t,{FN:()=>u,Nz:()=>n,Pe:()=>l,Rc:()=>s,Wc:()=>m,hJ:()=>c,my:()=>p,nh:()=>i,pQ:()=>d,q9:()=>h,rq:()=>o});var r=a(56621);async function s(e){let{data:t,error:a}=await r.N.schema("school").from("fee_payments").select(`
      *,
      student:IDCard(
        *,
        class:Class(name, section)
      )
    `).eq("receipt_url",e).single();if(a){if("PGRST116"===a.code)return null;throw a}return t}async function n(){let e=new Date().getMonth()+1,t=new Date().getFullYear(),{data:a,error:s}=await r.N.schema("school").from("IDCard").select(`
      *,
      class:Class(name, section)
    `);if(s)throw s;let{data:n,error:i}=await r.N.schema("school").from("fee_payments").select("*");if(i)throw i;let o=new Set((n||[]).filter(a=>{let r=new Date(a.payment_date),s=r.getMonth()+1,n=r.getFullYear();return s===e&&n===t&&"completed"===a.payment_status&&0===a.balance_remaining}).map(e=>e.student_id)),c=new Map;return n?.forEach(e=>{c.has(e.student_id)||c.set(e.student_id,[]),c.get(e.student_id).push(e)}),(a||[]).filter(e=>!o.has(e.id)).map(a=>{let r=c.get(a.id)||[],s=r.reduce((e,t)=>e+(parseFloat(t.amount_received)||0),0),n=r.reduce((e,t)=>e+(parseFloat(t.balance_remaining)||0),0),i=r.sort((e,t)=>new Date(t.payment_date).getTime()-new Date(e.payment_date).getTime())[0],o=r.find(a=>{let r=new Date(a.payment_date);return r.getMonth()+1===e&&r.getFullYear()===t}),d=n;return o||(d+=1e3),{...a,totalPaid:s,totalPending:d,lastPaymentDate:i?.payment_date,lastPaymentAmount:i?parseFloat(i.amount_received):null,pendingReason:o?`Outstanding balance: ₹${n}`:`No payment for ${e}/${t}`}})}async function i(){let{data:e,error:t}=await r.N.schema("school").from("fee_payments").select("amount_received, balance_remaining");if(t)throw t;let{data:a,error:s}=await r.N.schema("school").from("IDCard").select("id");if(s)throw s;let i=e?.reduce((e,t)=>e+t.amount_received,0)||0,o=e?.reduce((e,t)=>e+t.balance_remaining,0)||0,c=await n();return{totalCollected:i,totalPending:o,totalStudents:a?.length||0,studentsWithPending:c.length}}async function o(e){{let{data:t,error:a}=await r.N.schema("school").from("attendance").upsert(e,{onConflict:"student_id,attendance_date",ignoreDuplicates:!1}).select("*").single();if(a)throw a;return t}}async function c(e){{let{data:t,error:a}=await r.N.schema("school").from("attendance").upsert(e,{onConflict:"student_id,attendance_date",ignoreDuplicates:!1}).select("*");if(a)throw a;return t||[]}}async function d(e){{let{data:t,error:a}=await r.N.schema("school").from("attendance").select(`
        *,
        student:IDCard(*)
      `).eq("attendance_date",e).order("created_at",{ascending:!1});if(a)throw a;return t||[]}}async function l(e,t){{let a=r.N.schema("school").from("IDCard").select(`
        *,
        class:Class(name, section)
      `).order("student_name");t&&"all"!==t&&(a=a.eq("class_id",t));let{data:s,error:n}=await a;if(n)throw n;let{data:i,error:o}=await r.N.schema("school").from("attendance").select("*").eq("attendance_date",e);if(o)throw o;return s?.map(e=>{let t=i?.find(t=>t.student_id===e.id);return{...e,attendance:t}})||[]}}async function u(e,t){{let a=r.N.schema("school").from("IDCard").select("id",{count:"exact"});t&&"all"!==t&&(a=a.eq("class_id",t));let{data:s,error:n}=await a;if(n)throw n;let i=r.N.schema("school").from("attendance").select(`
        status,
        student:IDCard!inner(class_id)
      `).eq("attendance_date",e);t&&"all"!==t&&(i=i.eq("student.class_id",t));let{data:o,error:c}=await i;if(c)throw c;let d=s?.length||0,l=o?.filter(e=>"present"===e.status).length||0;return{totalStudents:d,presentCount:l,absentCount:o?.filter(e=>"absent"===e.status).length||0,attendancePercentage:Math.round(100*(d>0?l/d*100:0))/100}}}async function p(e=30){{let t=new Date,a=new Date;a.setDate(t.getDate()-e);let{data:s,error:n}=await r.N.schema("school").from("attendance").select("attendance_date, status").gte("attendance_date",a.toISOString().split("T")[0]).lte("attendance_date",t.toISOString().split("T")[0]).order("attendance_date");if(n)throw n;let{data:i,error:o}=await r.N.schema("school").from("IDCard").select("id",{count:"exact"});if(o)throw o;let c=i?.length||0;return Object.entries(s?.reduce((e,t)=>{let a=t.attendance_date;return e[a]||(e[a]={present:0,absent:0}),"present"===t.status?e[a].present++:e[a].absent++,e},{})||{}).map(([e,t])=>({date:e,presentCount:t.present,absentCount:t.absent,attendancePercentage:c>0?Math.round(t.present/c*1e4)/100:0}))}}async function h(e){{let{data:t,error:a}=await r.N.schema("school").from("attendance_messages").insert(e).select("*").single();if(a)throw a;return t}}async function m(e){{let t=r.N.schema("school").from("attendance_messages").select(`
        *,
        student:IDCard(*)
      `).order("created_at",{ascending:!1});e&&(t=t.eq("attendance_date",e));let{data:a,error:s}=await t;if(s)throw s;return a||[]}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},41512:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>m,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>p});var r={};a.r(r),a.d(r,{GET:()=>d});var s=a(96559),n=a(48088),i=a(37719),o=a(32190),c=a(6710);async function d(e){try{let{searchParams:t}=new URL(e.url),a=t.get("date"),r=t.get("class");if(!a)return o.NextResponse.json({error:"Date parameter is required"},{status:400});let s=await (0,c.FN)(a,r);return o.NextResponse.json(s)}catch(e){return console.error("Error fetching attendance statistics:",e),o.NextResponse.json({error:"Failed to fetch attendance statistics"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/attendance/statistics/route",pathname:"/api/attendance/statistics",filename:"route",bundlePath:"app/api/attendance/statistics/route"},resolvedPagePath:"/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/statistics/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:u,workUnitAsyncStorage:p,serverHooks:h}=l;function m(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:p})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,a)=>{"use strict";a.d(t,{N:()=>o});var r=a(66437);let s="https://ytfzqzjuhcdgcvvqihda.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0Znpxemp1aGNkZ2N2dnFpaGRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNjM1NjMsImV4cCI6MjA1MTYzOTU2M30.rXjVX0vZwZtD83oztSpcyY6331t6aitjgsvKuTgUzfg",i=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!s)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!n)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let o=(0,r.UU)(s,n);i&&(0,r.UU)(s,i)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,437,580],()=>a(41512));module.exports=r})();