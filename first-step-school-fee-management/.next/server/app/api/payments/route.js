(()=>{var e={};e.id=131,e.ids=[131],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29662:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>d,serverHooks:()=>y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>c,PUT:()=>l});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),u=r(56621);async function p(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"10"),a=t.get("sortBy")||"payment_date",n=t.get("sortOrder")||"desc",o=t.get("status"),p=t.get("method"),c=t.get("studentName"),l=t.get("className"),d=t.get("startDate"),m=t.get("endDate"),h=u.N.schema("school").from("fee_payments").select(`
        *,
        student:IDCard(
          *,
          class:Class(name, section)
        )
      `);o&&(h=h.eq("payment_status",o)),p&&(h=h.eq("payment_method",p)),d&&(h=h.gte("payment_date",d)),m&&(h=h.lte("payment_date",m)),h=h.order(a,{ascending:"asc"===n});let y=(r-1)*s;h=h.range(y,y+s-1);let{data:_,error:g,count:f}=await h;if(g)throw g;let x=_||[];return c&&(x=x.filter(e=>e.student?.student_name?.toLowerCase().includes(c.toLowerCase()))),l&&(x=x.filter(e=>e.student?.class?.name?.toLowerCase().includes(l.toLowerCase()))),i.NextResponse.json({data:x,pagination:{page:r,limit:s,total:f||0,totalPages:Math.ceil((f||0)/s)}})}catch(e){return console.error("Error fetching payments:",e),i.NextResponse.json({error:"Failed to fetch payments"},{status:500})}}async function c(e){try{let t=await e.json();for(let e of["student_id","amount_received","payment_date","payment_method","payment_status"])if(!t[e])return i.NextResponse.json({error:`${e} is required`},{status:400});t.amount_received=parseFloat(t.amount_received),t.balance_remaining=parseFloat(t.balance_remaining||0);let r=new Date(t.payment_date);t.fee_month=r.getMonth()+1,t.fee_year=r.getFullYear();let s=crypto.randomUUID(),a=`/receipt/${s}`,{data:n,error:o}=await u.N.schema("school").from("fee_payments").insert({...t,receipt_url:a}).select("*").single();if(o)throw o;return i.NextResponse.json(n)}catch(e){return console.error("Error creating payment:",e),i.NextResponse.json({error:"Failed to create payment"},{status:500})}}async function l(e){try{let{id:t,updated_by:r="system",update_reason:s,...a}=await e.json();if(!t)return i.NextResponse.json({error:"Payment ID is required"},{status:400});let{data:n,error:o}=await u.N.schema("school").from("fee_payments").select("*").eq("id",t).single();if(o||!n)return i.NextResponse.json({error:"Payment record not found"},{status:404});let p={...a};if(p.amount_received&&(p.amount_received=parseFloat(p.amount_received)),void 0!==p.balance_remaining&&(p.balance_remaining=parseFloat(p.balance_remaining)),p.payment_date){let e=new Date(p.payment_date);p.fee_month=e.getMonth()+1,p.fee_year=e.getFullYear()}p.has_updates=!0;let{data:c,error:l}=await u.N.schema("school").from("fee_payments").update(p).eq("id",t).select("*").single();if(l)throw l;let d=[];for(let[e,o]of Object.entries(a))n[e]!==o&&d.push({fee_payment_id:t,field_name:e,old_value:n[e]?.toString()||null,new_value:o?.toString()||null,updated_by:r,update_reason:s});if(d.length>0){let{error:e}=await u.N.schema("school").from("fee_history_update").insert(d);e&&console.error("Error creating history entries:",e)}return i.NextResponse.json(c)}catch(e){return console.error("Error updating payment:",e),i.NextResponse.json({error:"Failed to update payment"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/payments/route",pathname:"/api/payments",filename:"route",bundlePath:"app/api/payments/route"},resolvedPagePath:"/home/<USER>/school/first-step-school-fee-management/src/app/api/payments/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:y}=d;function _(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var s=r(66437);let a="https://ytfzqzjuhcdgcvvqihda.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0Znpxemp1aGNkZ2N2dnFpaGRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNjM1NjMsImV4cCI6MjA1MTYzOTU2M30.rXjVX0vZwZtD83oztSpcyY6331t6aitjgsvKuTgUzfg",o=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!n)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let i=(0,s.UU)(a,n);o&&(0,s.UU)(a,o)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,437,580],()=>r(29662));module.exports=s})();