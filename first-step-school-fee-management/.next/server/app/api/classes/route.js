(()=>{var e={};e.id=786,e.ids=[786],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10583:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var i=t(96559),o=t(48088),a=t(37719),u=t(32190),n=t(56621);async function p(){try{let{data:e,error:r}=await n.N.schema("school").from("IDCard").select("class_id").order("class_id");if(r)throw r;let t=[...new Set(e.map(e=>e.class_id).filter(Boolean))];return u.NextResponse.json(t)}catch(e){return console.error("Error fetching classes:",e),u.NextResponse.json({error:"Failed to fetch classes"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/classes/route",pathname:"/api/classes",filename:"route",bundlePath:"app/api/classes/route"},resolvedPagePath:"/home/<USER>/school/first-step-school-fee-management/src/app/api/classes/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=c;function h(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,r,t)=>{"use strict";t.d(r,{N:()=>u});var s=t(66437);let i="https://ytfzqzjuhcdgcvvqihda.supabase.co",o="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0Znpxemp1aGNkZ2N2dnFpaGRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNjM1NjMsImV4cCI6MjA1MTYzOTU2M30.rXjVX0vZwZtD83oztSpcyY6331t6aitjgsvKuTgUzfg",a=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!i)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!o)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let u=(0,s.UU)(i,o);a&&(0,s.UU)(i,a)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,437,580],()=>t(10583));module.exports=s})();