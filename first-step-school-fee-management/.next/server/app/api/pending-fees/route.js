(()=>{var e={};e.id=191,e.ids=[191],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6710:(e,t,a)=>{"use strict";a.d(t,{FN:()=>u,Nz:()=>s,Pe:()=>l,Rc:()=>r,Wc:()=>h,hJ:()=>c,my:()=>p,nh:()=>i,pQ:()=>d,q9:()=>m,rq:()=>o});var n=a(56621);async function r(e){let{data:t,error:a}=await n.N.schema("school").from("fee_payments").select(`
      *,
      student:IDCard(
        *,
        class:Class(name, section)
      )
    `).eq("receipt_url",e).single();if(a){if("PGRST116"===a.code)return null;throw a}return t}async function s(){let e=new Date().getMonth()+1,t=new Date().getFullYear(),{data:a,error:r}=await n.N.schema("school").from("IDCard").select(`
      *,
      class:Class(name, section)
    `);if(r)throw r;let{data:s,error:i}=await n.N.schema("school").from("fee_payments").select("*");if(i)throw i;let o=new Set((s||[]).filter(a=>{let n=new Date(a.payment_date),r=n.getMonth()+1,s=n.getFullYear();return r===e&&s===t&&"completed"===a.payment_status&&0===a.balance_remaining}).map(e=>e.student_id)),c=new Map;return s?.forEach(e=>{c.has(e.student_id)||c.set(e.student_id,[]),c.get(e.student_id).push(e)}),(a||[]).filter(e=>!o.has(e.id)).map(a=>{let n=c.get(a.id)||[],r=n.reduce((e,t)=>e+(parseFloat(t.amount_received)||0),0),s=n.reduce((e,t)=>e+(parseFloat(t.balance_remaining)||0),0),i=n.sort((e,t)=>new Date(t.payment_date).getTime()-new Date(e.payment_date).getTime())[0],o=n.find(a=>{let n=new Date(a.payment_date);return n.getMonth()+1===e&&n.getFullYear()===t}),d=s;return o||(d+=1e3),{...a,totalPaid:r,totalPending:d,lastPaymentDate:i?.payment_date,lastPaymentAmount:i?parseFloat(i.amount_received):null,pendingReason:o?`Outstanding balance: ₹${s}`:`No payment for ${e}/${t}`}})}async function i(){let{data:e,error:t}=await n.N.schema("school").from("fee_payments").select("amount_received, balance_remaining");if(t)throw t;let{data:a,error:r}=await n.N.schema("school").from("IDCard").select("id");if(r)throw r;let i=e?.reduce((e,t)=>e+t.amount_received,0)||0,o=e?.reduce((e,t)=>e+t.balance_remaining,0)||0,c=await s();return{totalCollected:i,totalPending:o,totalStudents:a?.length||0,studentsWithPending:c.length}}async function o(e){{let{data:t,error:a}=await n.N.schema("school").from("attendance").upsert(e,{onConflict:"student_id,attendance_date",ignoreDuplicates:!1}).select("*").single();if(a)throw a;return t}}async function c(e){{let{data:t,error:a}=await n.N.schema("school").from("attendance").upsert(e,{onConflict:"student_id,attendance_date",ignoreDuplicates:!1}).select("*");if(a)throw a;return t||[]}}async function d(e){{let{data:t,error:a}=await n.N.schema("school").from("attendance").select(`
        *,
        student:IDCard(*)
      `).eq("attendance_date",e).order("created_at",{ascending:!1});if(a)throw a;return t||[]}}async function l(e,t){{let a=n.N.schema("school").from("IDCard").select(`
        *,
        class:Class(name, section)
      `).order("student_name");t&&"all"!==t&&(a=a.eq("class_id",t));let{data:r,error:s}=await a;if(s)throw s;let{data:i,error:o}=await n.N.schema("school").from("attendance").select("*").eq("attendance_date",e);if(o)throw o;return r?.map(e=>{let t=i?.find(t=>t.student_id===e.id);return{...e,attendance:t}})||[]}}async function u(e,t){{let a=n.N.schema("school").from("IDCard").select("id",{count:"exact"});t&&"all"!==t&&(a=a.eq("class_id",t));let{data:r,error:s}=await a;if(s)throw s;let i=n.N.schema("school").from("attendance").select(`
        status,
        student:IDCard!inner(class_id)
      `).eq("attendance_date",e);t&&"all"!==t&&(i=i.eq("student.class_id",t));let{data:o,error:c}=await i;if(c)throw c;let d=r?.length||0,l=o?.filter(e=>"present"===e.status).length||0;return{totalStudents:d,presentCount:l,absentCount:o?.filter(e=>"absent"===e.status).length||0,attendancePercentage:Math.round(100*(d>0?l/d*100:0))/100}}}async function p(e=30){{let t=new Date,a=new Date;a.setDate(t.getDate()-e);let{data:r,error:s}=await n.N.schema("school").from("attendance").select("attendance_date, status").gte("attendance_date",a.toISOString().split("T")[0]).lte("attendance_date",t.toISOString().split("T")[0]).order("attendance_date");if(s)throw s;let{data:i,error:o}=await n.N.schema("school").from("IDCard").select("id",{count:"exact"});if(o)throw o;let c=i?.length||0;return Object.entries(r?.reduce((e,t)=>{let a=t.attendance_date;return e[a]||(e[a]={present:0,absent:0}),"present"===t.status?e[a].present++:e[a].absent++,e},{})||{}).map(([e,t])=>({date:e,presentCount:t.present,absentCount:t.absent,attendancePercentage:c>0?Math.round(t.present/c*1e4)/100:0}))}}async function m(e){{let{data:t,error:a}=await n.N.schema("school").from("attendance_messages").insert(e).select("*").single();if(a)throw a;return t}}async function h(e){{let t=n.N.schema("school").from("attendance_messages").select(`
        *,
        student:IDCard(*)
      `).order("created_at",{ascending:!1});e&&(t=t.eq("attendance_date",e));let{data:a,error:r}=await t;if(r)throw r;return a||[]}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,a)=>{"use strict";a.d(t,{N:()=>o});var n=a(66437);let r="https://ytfzqzjuhcdgcvvqihda.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0Znpxemp1aGNkZ2N2dnFpaGRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNjM1NjMsImV4cCI6MjA1MTYzOTU2M30.rXjVX0vZwZtD83oztSpcyY6331t6aitjgsvKuTgUzfg",i=process.env.SUPABASE_SERVICE_ROLE_KEY;if(!r)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!s)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let o=(0,n.UU)(r,s);i&&(0,n.UU)(r,i)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},78570:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var n={};a.r(n),a.d(n,{GET:()=>l});var r=a(96559),s=a(48088),i=a(37719),o=a(32190),c=a(6710),d=a(56621);async function l(e){try{let{searchParams:t}=new URL(e.url),a="true"===t.get("summary"),n=t.get("month"),r=t.get("year");if(a){let e=await (0,c.nh)();return o.NextResponse.json(e)}if(n&&r){let e=await u(parseInt(n),parseInt(r));return o.NextResponse.json(e)}{let e=await (0,c.Nz)();return o.NextResponse.json(e)}}catch(e){return console.error("Error fetching pending fees:",e),o.NextResponse.json({error:"Failed to fetch pending fees"},{status:500})}}async function u(e,t){let{data:a,error:n}=await d.N.schema("school").from("IDCard").select(`
      *,
      class:Class(name, section)
    `);if(n)throw n;let{data:r,error:s}=await d.N.schema("school").from("fee_payments").select("*");if(s)throw s;let i=new Set((r||[]).filter(a=>{let n=new Date(a.payment_date),r=n.getMonth()+1,s=n.getFullYear();return r===e&&s===t&&"completed"===a.payment_status&&0===a.balance_remaining}).map(e=>e.student_id)),o=new Map;return r?.forEach(e=>{o.has(e.student_id)||o.set(e.student_id,[]),o.get(e.student_id).push(e)}),(a||[]).filter(e=>!i.has(e.id)).map(a=>{let n=o.get(a.id)||[],r=n.find(a=>{let n=new Date(a.payment_date);return n.getMonth()+1===e&&n.getFullYear()===t}),s=n.reduce((e,t)=>e+(parseFloat(t.amount_received.toString())||0),0),i=n.sort((e,t)=>new Date(t.payment_date).getTime()-new Date(e.payment_date).getTime())[0];return{...a,totalPaid:s,totalPending:r?parseFloat(r.balance_remaining):1e3,lastPaymentDate:i?.payment_date,lastPaymentAmount:i?parseFloat(i.amount_received):null,pendingMonth:e,pendingYear:t,pendingReason:r?`Outstanding balance: ₹${r.balance_remaining}`:`No payment record for ${e}/${t}`}})}let p=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/pending-fees/route",pathname:"/api/pending-fees",filename:"route",bundlePath:"app/api/pending-fees/route"},resolvedPagePath:"/home/<USER>/school/first-step-school-fee-management/src/app/api/pending-fees/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:f}=p;function g(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[447,437,580],()=>a(78570));module.exports=n})();