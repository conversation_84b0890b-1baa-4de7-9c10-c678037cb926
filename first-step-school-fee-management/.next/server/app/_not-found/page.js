(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14094:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>m,routeModule:()=>h,tree:()=>l});var s=r(65239),n=r(48088),o=r(88170),i=r.n(o),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,54413)),"/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/home/<USER>/school/first-step-school-fee-management/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,m=[],c={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26036:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30391:()=>{},33596:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},33873:e=>{"use strict";e.exports=require("path")},35967:()=>{},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),n=r(4536),o=r.n(n);function i(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-6xl font-bold text-gray-900 mb-4",children:"404"}),(0,s.jsx)("h2",{className:"text-2xl font-semibold text-gray-700 mb-4",children:"Receipt Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-8 max-w-md",children:"The receipt you're looking for doesn't exist or may have been removed."}),(0,s.jsx)(o(),{href:"/",className:"inline-block bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors",children:"Go to Fee Management"})]})})}},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73300:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},75452:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>i});var s=r(37413),n=r(7339),o=r.n(n);r(61135);let i={title:"First Step School - Management System",description:"School management system for First Step School, Saurabh Vihar, Jaitpur, Delhi. Manage attendance, fees, and student records."};function a({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:o().className,children:e})})}},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,287],()=>r(14094));module.exports=s})();