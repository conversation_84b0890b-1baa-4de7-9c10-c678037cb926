(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{8587:(e,t,s)=>{Promise.resolve().then(s.bind(s,26188))},26188:(e,t,s)=>{"use strict";s.d(t,{default:()=>ex});var a=s(95155),l=s(12115),n=s(72713),r=s(55670),d=s(81586),i=s(57434),c=s(14186),o=s(62177),m=s(48778),x=s(71153),u=s(26681),h=s(55647),g=s(49509);let p="https://ytfzqzjuhcdgcvvqihda.supabase.co",y="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl0Znpxemp1aGNkZ2N2dnFpaGRhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzYwNjM1NjMsImV4cCI6MjA1MTYzOTU2M30.rXjVX0vZwZtD83oztSpcyY6331t6aitjgsvKuTgUzfg",b=g.env.SUPABASE_SERVICE_ROLE_KEY;if(!p)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!y)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");async function j(){{let e=await fetch("/api/classes-with-names");if(!e.ok)throw Error("Failed to fetch classes");return e.json()}}async function f(e){{let t=await fetch("/api/students?class=".concat(encodeURIComponent(e)));if(!t.ok)throw Error("Failed to fetch students");return t.json()}}async function N(e){{let t=await fetch("/api/payments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("Failed to create payment");return t.json()}}async function v(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"system",a=arguments.length>3?arguments[3]:void 0,l=await fetch("/api/payments",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({id:e,...t,updated_by:s,update_reason:a})});if(!l.ok)throw Error("Failed to update payment");return l.json()}(0,h.UU)(p,y),b&&(0,h.UU)(p,b);var w=s(55868),_=s(69074),S=s(66516);let C=x.z.object({student_id:x.z.string().min(1,"Please select a student"),amount_received:x.z.number().min(.01,"Amount must be greater than 0"),payment_date:x.z.string().min(1,"Payment date is required"),payment_method:x.z.enum(["cash","card","upi","bank_transfer","cheque"]),balance_remaining:x.z.number().min(0,"Balance cannot be negative"),payment_status:x.z.enum(["completed","partial","pending"]),notes:x.z.string().optional()});function k(){let[e,t]=(0,l.useState)([]),[s,n]=(0,l.useState)([]),[r,c]=(0,l.useState)(""),[x,h]=(0,l.useState)(!1),[g,p]=(0,l.useState)(""),{register:y,handleSubmit:b,watch:v,setValue:k,reset:A,formState:{errors:P}}=(0,o.mN)({resolver:(0,m.u)(C),defaultValues:{payment_date:(0,u.GP)(new Date,"yyyy-MM-dd"),payment_method:"cash",payment_status:"completed",balance_remaining:0}}),M=v("student_id"),F=s.find(e=>e.id===M),D=e.find(e=>e.id===r);(0,l.useEffect)(()=>{E()},[]),(0,l.useEffect)(()=>{r&&L(r)},[r]);let E=async()=>{try{let e=await j();t(e)}catch(e){console.error("Error loading classes:",e)}},L=async e=>{try{let t=await f(e);n(t)}catch(e){console.error("Error loading students:",e)}},R=async e=>{h(!0);try{let t=await N(e);p("".concat(window.location.origin).concat(t.receipt_url)),A(),c(""),n([])}catch(e){console.error("Error creating payment:",e),alert("Error creating payment. Please try again.")}finally{h(!1)}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("form",{onSubmit:b(R),className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Class"}),(0,a.jsxs)("select",{value:r,onChange:e=>{c(e.target.value),k("student_id","")},className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",children:[(0,a.jsx)("option",{value:"",className:"text-gray-500",children:"Choose a class..."}),e.map(e=>(0,a.jsxs)("option",{value:e.id,className:"text-gray-900",children:[e.name," - ",e.section]},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Select Student"}),(0,a.jsxs)("select",{...y("student_id"),disabled:!r,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100 bg-white text-gray-900",children:[(0,a.jsx)("option",{value:"",className:"text-gray-500",children:"Choose a student..."}),s.map(e=>(0,a.jsxs)("option",{value:e.id,className:"text-gray-900",children:[e.student_name," - ",e.father_name]},e.id))]}),P.student_id&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:P.student_id.message})]}),F&&(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-900 mb-2",children:"Student Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Student:"})," ",F.student_name]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Class:"})," ",D?"".concat(D.name," - ").concat(D.section):F.class_id]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Father:"})," ",F.father_name]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Mother:"})," ",F.mother_name]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Father Mobile:"})," ",F.father_mobile||"N/A"]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:"Mother Mobile:"})," ",F.mother_mobile||"N/A"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(w.A,{className:"inline w-4 h-4 mr-1"}),"Amount Received"]}),(0,a.jsx)("input",{type:"number",step:"0.01",...y("amount_received",{valueAsNumber:!0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500",placeholder:"0.00"}),P.amount_received&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:P.amount_received.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(_.A,{className:"inline w-4 h-4 mr-1"}),"Payment Date"]}),(0,a.jsx)("input",{type:"date",...y("payment_date"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900"}),P.payment_date&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:P.payment_date.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(d.A,{className:"inline w-4 h-4 mr-1"}),"Payment Method"]}),(0,a.jsxs)("select",{...y("payment_method"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",children:[(0,a.jsx)("option",{value:"cash",className:"text-gray-900",children:"Cash"}),(0,a.jsx)("option",{value:"card",className:"text-gray-900",children:"Card"}),(0,a.jsx)("option",{value:"upi",className:"text-gray-900",children:"UPI"}),(0,a.jsx)("option",{value:"bank_transfer",className:"text-gray-900",children:"Bank Transfer"}),(0,a.jsx)("option",{value:"cheque",className:"text-gray-900",children:"Cheque"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Balance Remaining"}),(0,a.jsx)("input",{type:"number",step:"0.01",...y("balance_remaining",{valueAsNumber:!0}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500",placeholder:"0.00"}),P.balance_remaining&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:P.balance_remaining.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Payment Status"}),(0,a.jsxs)("select",{...y("payment_status"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900",children:[(0,a.jsx)("option",{value:"completed",className:"text-gray-900",children:"Completed"}),(0,a.jsx)("option",{value:"partial",className:"text-gray-900",children:"Partial"}),(0,a.jsx)("option",{value:"pending",className:"text-gray-900",children:"Pending"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(i.A,{className:"inline w-4 h-4 mr-1"}),"Additional Notes"]}),(0,a.jsx)("textarea",{...y("notes"),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-gray-900 placeholder-gray-500",placeholder:"Any additional notes about the payment..."})]}),(0,a.jsx)("button",{type:"submit",disabled:x,className:"w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:x?"Processing...":"Record Payment & Generate Receipt"})]}),g&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-green-900 mb-4",children:"Receipt Generated Successfully!"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-green-700 mb-2",children:"Receipt URL:"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("input",{type:"text",value:g,readOnly:!0,className:"flex-1 px-3 py-2 border border-green-300 rounded-md bg-white"}),(0,a.jsx)("button",{onClick:()=>navigator.clipboard.writeText(g),className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"Copy"})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("a",{href:g,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:[(0,a.jsx)(i.A,{className:"w-4 h-4"}),"View Receipt"]}),(0,a.jsxs)("button",{onClick:()=>{if(g&&F){let e="Fee Receipt - ".concat(F.student_name,"\n\nDear Parent,\n\nYour fee payment has been recorded. Please view and download your receipt:\n\n").concat(g,"\n\nThank you!\n").concat("First Step School"),t="https://wa.me/?text=".concat(encodeURIComponent(e));window.open(t,"_blank")}},className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[(0,a.jsx)(S.A,{className:"w-4 h-4"}),"Share on WhatsApp"]})]})]})]})]})}var A=s(66932),P=s(91788),M=s(4229),F=s(54416),D=s(13717),E=s(29676),L=s(92657),R=s(42355),T=s(13052);function U(){let[e,t]=(0,l.useState)([]),[s,n]=(0,l.useState)(!0),[r,d]=(0,l.useState)(1),[i,c]=(0,l.useState)(1),[o,m]=(0,l.useState)(!1),[x,h]=(0,l.useState)(null),[g,p]=(0,l.useState)({}),[y,b]=(0,l.useState)(null),[j,f]=(0,l.useState)([]),[N,w]=(0,l.useState)(""),[_,S]=(0,l.useState)({studentName:"",className:"",status:"",method:"",startDate:"",endDate:""}),C=async()=>{n(!0);try{let e=new URLSearchParams({page:r.toString(),limit:"10",sortBy:"payment_date",sortOrder:"desc",..._.status&&{status:_.status},..._.method&&{method:_.method},..._.studentName&&{studentName:_.studentName},..._.className&&{className:_.className},..._.startDate&&{startDate:_.startDate},..._.endDate&&{endDate:_.endDate}}),s=await fetch("/api/payments?".concat(e));if(!s.ok)throw Error("Failed to fetch payments");let a=await s.json();t(a.data),c(a.pagination.totalPages)}catch(e){console.error("Error fetching payments:",e)}finally{n(!1)}};(0,l.useEffect)(()=>{C()},[r,_]);let k=(e,t)=>{S(s=>({...s,[e]:t})),d(1)},U=e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"partial":return"bg-yellow-100 text-yellow-800";case"pending":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},G=e=>{switch(e){case"bank_transfer":return"Bank Transfer";case"upi":return"UPI";default:return e.charAt(0).toUpperCase()+e.slice(1)}},I=e=>{h(e.id),p({amount_received:e.amount_received,payment_date:e.payment_date,payment_method:e.payment_method,payment_status:e.payment_status,balance_remaining:e.balance_remaining,notes:e.notes}),w("")},z=async()=>{if(!x||!N.trim())return void alert("Please provide a reason for the update");try{await v(x,g,"admin",N),h(null),p({}),w(""),C()}catch(e){console.error("Error updating payment:",e),alert("Failed to update payment record")}},O=()=>{h(null),p({}),w("")},B=async e=>{try{let t=await fetch("/api/fee-history?fee_payment_id=".concat(e));if(!t.ok)throw Error("Failed to fetch history");let s=await t.json();f(s),b(e)}catch(e){console.error("Error fetching history:",e),alert("Failed to fetch payment history")}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Fee Payment Records"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"View and manage all fee payment submissions"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>m(!o),className:"flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700",children:[(0,a.jsx)(A.A,{className:"w-4 h-4"}),"Filters"]}),(0,a.jsxs)("button",{onClick:()=>{let t=new Blob([[["Date","Student Name","Class","Amount","Method","Status","Balance","Notes"],...e.map(e=>{var t,s;return[(0,u.GP)(new Date(e.payment_date),"dd/MM/yyyy"),(null==(t=e.student)?void 0:t.student_name)||"",(null==(s=e.student)?void 0:s.class)?"".concat(e.student.class.name," ").concat(e.student.class.section):"",e.amount_received,e.payment_method,e.payment_status,e.balance_remaining,e.notes||""]})].map(e=>e.map(e=>'"'.concat(e,'"')).join(",")).join("\n")],{type:"text/csv"}),s=window.URL.createObjectURL(t),a=document.createElement("a");a.href=s,a.download="fee-records-".concat((0,u.GP)(new Date,"yyyy-MM-dd"),".csv"),a.click(),window.URL.revokeObjectURL(s)},className:"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:[(0,a.jsx)(P.A,{className:"w-4 h-4"}),"Export CSV"]})]})]}),o&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg border",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Student Name"}),(0,a.jsx)("input",{type:"text",value:_.studentName,onChange:e=>k("studentName",e.target.value),placeholder:"Search by student name...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Class"}),(0,a.jsx)("input",{type:"text",value:_.className,onChange:e=>k("className",e.target.value),placeholder:"Search by class...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Status"}),(0,a.jsxs)("select",{value:_.status,onChange:e=>k("status",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"",children:"All Statuses"}),(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"partial",children:"Partial"}),(0,a.jsx)("option",{value:"pending",children:"Pending"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Payment Method"}),(0,a.jsxs)("select",{value:_.method,onChange:e=>k("method",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white",children:[(0,a.jsx)("option",{value:"",children:"All Methods"}),(0,a.jsx)("option",{value:"cash",children:"Cash"}),(0,a.jsx)("option",{value:"card",children:"Card"}),(0,a.jsx)("option",{value:"upi",children:"UPI"}),(0,a.jsx)("option",{value:"bank_transfer",children:"Bank Transfer"}),(0,a.jsx)("option",{value:"cheque",children:"Cheque"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Start Date"}),(0,a.jsx)("input",{type:"date",value:_.startDate,onChange:e=>k("startDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"End Date"}),(0,a.jsx)("input",{type:"date",value:_.endDate,onChange:e=>k("endDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"})]})]}),(0,a.jsx)("div",{className:"mt-4 flex justify-end",children:(0,a.jsx)("button",{onClick:()=>{S({studentName:"",className:"",status:"",method:"",startDate:"",endDate:""}),d(1)},className:"px-4 py-2 text-sm text-gray-600 hover:text-gray-800",children:"Clear All Filters"})})]}),(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:s?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading fee records..."})]}):0===e.length?(0,a.jsx)("div",{className:"p-8 text-center",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No fee records found matching your criteria."})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Class"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Method"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Balance"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>{var t,s;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:x===e.id?(0,a.jsx)("input",{type:"date",value:g.payment_date||"",onChange:e=>p(t=>({...t,payment_date:e.target.value})),className:"w-full px-2 py-1 border border-gray-300 rounded text-gray-900"}):(0,u.GP)(new Date(e.payment_date),"dd/MM/yyyy")}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(null==(t=e.student)?void 0:t.student_name)||"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(null==(s=e.student)?void 0:s.class)?"".concat(e.student.class.name," ").concat(e.student.class.section):"N/A"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:x===e.id?(0,a.jsx)("input",{type:"number",step:"0.01",value:g.amount_received||"",onChange:e=>p(t=>({...t,amount_received:parseFloat(e.target.value)})),className:"w-full px-2 py-1 border border-gray-300 rounded text-gray-900"}):"₹".concat(e.amount_received.toLocaleString())}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:x===e.id?(0,a.jsxs)("select",{value:g.payment_method||"",onChange:e=>p(t=>({...t,payment_method:e.target.value})),className:"w-full px-2 py-1 border border-gray-300 rounded text-gray-900",children:[(0,a.jsx)("option",{value:"cash",children:"Cash"}),(0,a.jsx)("option",{value:"card",children:"Card"}),(0,a.jsx)("option",{value:"upi",children:"UPI"}),(0,a.jsx)("option",{value:"bank_transfer",children:"Bank Transfer"}),(0,a.jsx)("option",{value:"cheque",children:"Cheque"})]}):G(e.payment_method)}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:x===e.id?(0,a.jsxs)("select",{value:g.payment_status||"",onChange:e=>p(t=>({...t,payment_status:e.target.value})),className:"w-full px-2 py-1 border border-gray-300 rounded text-gray-900",children:[(0,a.jsx)("option",{value:"completed",children:"Completed"}),(0,a.jsx)("option",{value:"partial",children:"Partial"}),(0,a.jsx)("option",{value:"pending",children:"Pending"})]}):(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(U(e.payment_status)),children:e.payment_status})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:x===e.id?(0,a.jsx)("input",{type:"number",step:"0.01",value:g.balance_remaining||"",onChange:e=>p(t=>({...t,balance_remaining:parseFloat(e.target.value)})),className:"w-full px-2 py-1 border border-gray-300 rounded text-gray-900"}):"₹".concat(e.balance_remaining.toLocaleString())}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsx)("div",{className:"flex items-center gap-2",children:x===e.id?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:z,className:"text-green-600 hover:text-green-900",title:"Save changes",children:(0,a.jsx)(M.A,{className:"w-4 h-4"})}),(0,a.jsx)("button",{onClick:O,className:"text-red-600 hover:text-red-900",title:"Cancel edit",children:(0,a.jsx)(F.A,{className:"w-4 h-4"})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>I(e),className:"text-blue-600 hover:text-blue-900",title:"Edit record",children:(0,a.jsx)(D.A,{className:"w-4 h-4"})}),e.has_updates&&(0,a.jsx)("button",{onClick:()=>B(e.id),className:"text-purple-600 hover:text-purple-900",title:"View history",children:(0,a.jsx)(E.A,{className:"w-4 h-4"})}),(0,a.jsx)("a",{href:e.receipt_url,target:"_blank",rel:"noopener noreferrer",className:"text-gray-600 hover:text-gray-900",title:"View receipt",children:(0,a.jsx)(L.A,{className:"w-4 h-4"})})]})})})]},e.id)})})]})}),i>1&&(0,a.jsxs)("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)("button",{onClick:()=>d(e=>Math.max(e-1,1)),disabled:1===r,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,a.jsx)("button",{onClick:()=>d(e=>Math.min(e+1,i)),disabled:r===i,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Showing page ",(0,a.jsx)("span",{className:"font-medium",children:r})," of"," ",(0,a.jsx)("span",{className:"font-medium",children:i})]})}),(0,a.jsx)("div",{children:(0,a.jsxs)("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[(0,a.jsx)("button",{onClick:()=>d(e=>Math.max(e-1,1)),disabled:1===r,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,a.jsx)(R.A,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:()=>d(e=>Math.min(e+1,i)),disabled:r===i,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,a.jsx)(T.A,{className:"h-5 w-5"})})]})})]})]})]})}),x&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Update Reason"}),(0,a.jsx)("textarea",{value:N,onChange:e=>w(e.target.value),placeholder:"Please provide a reason for this update...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900",rows:3}),(0,a.jsxs)("div",{className:"flex justify-end gap-2 mt-4",children:[(0,a.jsx)("button",{onClick:O,className:"px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{onClick:z,disabled:!N.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Save Changes"})]})]})}),y&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Payment History"}),(0,a.jsx)("button",{onClick:()=>b(null),className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(F.A,{className:"w-6 h-6"})})]}),0===j.length?(0,a.jsx)("p",{className:"text-gray-500",children:"No history records found."}):(0,a.jsx)("div",{className:"space-y-4",children:j.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:["Field: ",e.field_name]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:(0,u.GP)(new Date(e.created_at),"dd/MM/yyyy HH:mm")})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Old Value:"}),(0,a.jsx)("div",{className:"text-gray-900 bg-red-50 px-2 py-1 rounded",children:e.old_value||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"New Value:"}),(0,a.jsx)("div",{className:"text-gray-900 bg-green-50 px-2 py-1 rounded",children:e.new_value||"N/A"})]})]}),(0,a.jsxs)("div",{className:"mt-2 text-sm",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Updated by:"}),(0,a.jsx)("span",{className:"text-gray-900 ml-1",children:e.updated_by})]}),e.update_reason&&(0,a.jsxs)("div",{className:"mt-2 text-sm",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Reason:"}),(0,a.jsx)("div",{className:"text-gray-900 bg-gray-50 px-2 py-1 rounded mt-1",children:e.update_reason})]})]},e.id))})]})})]})}var G=s(85339),I=s(47924),z=s(19420);function O(){var e;let[t,s]=(0,l.useState)([]),[n,r]=(0,l.useState)(!0),[i,c]=(0,l.useState)(""),[o,m]=(0,l.useState)(""),[x,h]=(0,l.useState)(null),[g,p]=(0,l.useState)(new Date().getMonth()+1),[y,b]=(0,l.useState)(new Date().getFullYear()),[j,f]=(0,l.useState)(!1),N=[{value:1,label:"January"},{value:2,label:"February"},{value:3,label:"March"},{value:4,label:"April"},{value:5,label:"May"},{value:6,label:"June"},{value:7,label:"July"},{value:8,label:"August"},{value:9,label:"September"},{value:10,label:"October"},{value:11,label:"November"},{value:12,label:"December"}],v=Array.from({length:5},(e,t)=>new Date().getFullYear()-2+t),w=async()=>{r(!0);try{let e="/api/pending-fees";j&&(e+="?month=".concat(g,"&year=").concat(y));let t=await fetch(e);if(!t.ok)throw Error("Failed to fetch pending fees");let a=await t.json();s(a)}catch(e){console.error("Error fetching pending fees:",e)}finally{r(!1)}};(0,l.useEffect)(()=>{w()},[g,y,j]);let S=t.filter(e=>{var t;let s=e.student_name.toLowerCase().includes(i.toLowerCase())||e.father_name.toLowerCase().includes(i.toLowerCase())||e.mother_name.toLowerCase().includes(i.toLowerCase()),a=!o||(null==(t=e.class)?void 0:t.name.toLowerCase().includes(o.toLowerCase()));return s&&a}),C=e=>{h(e)},k=e=>{var t,s,a;let l="Dear Parent,\n\nThis is a gentle reminder that the fee payment for ".concat(e.student_name," (Class: ").concat(null==(t=e.class)?void 0:t.name," ").concat(null==(s=e.class)?void 0:s.section,") is pending.\n\nOutstanding Amount: ₹").concat(e.totalPending.toLocaleString(),"\n").concat(e.lastPaymentDate?"Last Payment: ₹".concat(null==(a=e.lastPaymentAmount)?void 0:a.toLocaleString()," on ").concat((0,u.GP)(new Date(e.lastPaymentDate),"dd/MM/yyyy")):"No previous payments found","\n\nPlease make the payment at your earliest convenience.\n\nThank you,\n").concat("First Step School"),n=e.father_mobile||e.mother_mobile;if(n){let e="https://wa.me/".concat(n.replace(/\D/g,""),"?text=").concat(encodeURIComponent(l));window.open(e,"_blank")}},A=S.reduce((e,t)=>e+t.totalPending,0);return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 p-4 lg:p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 lg:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3",children:[(0,a.jsx)(G.A,{className:"w-6 h-6 text-red-600 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg lg:text-xl font-medium text-red-900",children:["Pending Fees Summary",j&&(0,a.jsxs)("span",{className:"block sm:inline text-sm font-normal sm:ml-2 mt-1 sm:mt-0",children:["for ",null==(e=N.find(e=>e.value===g))?void 0:e.label," ",y]})]}),(0,a.jsxs)("p",{className:"text-red-700 text-sm lg:text-base",children:[S.length," students have pending fees totaling ₹",A.toLocaleString()]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4 lg:p-6",children:[(0,a.jsx)("div",{className:"flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full lg:w-auto",children:[(0,a.jsxs)("button",{onClick:()=>f(!j),className:"flex items-center gap-2 px-4 py-2 rounded-md border transition-colors text-sm ".concat(j?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:[(0,a.jsx)(_.A,{className:"w-4 h-4"}),j?"Show All Pending":"Filter by Month"]}),j&&(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2",children:[(0,a.jsx)("select",{value:g,onChange:e=>p(parseInt(e.target.value)),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 text-sm",children:N.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,a.jsx)("select",{value:y,onChange:e=>b(parseInt(e.target.value)),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900",children:v.map(e=>(0,a.jsx)("option",{value:e,children:e},e))})]})]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by student name or parent name...",value:i,onChange:e=>c(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"})]})}),(0,a.jsx)("div",{className:"w-full sm:w-48",children:(0,a.jsx)("input",{type:"text",placeholder:"Filter by class...",value:o,onChange:e=>m(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"})})]}),(0,a.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg overflow-hidden",children:n?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-500",children:"Loading pending fees..."})]}):0===S.length?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)(G.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-500",children:0===t.length?"Great! No students have pending fees.":"No students found matching your search criteria."})]}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Student Details"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Class"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Total Paid"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Pending Amount"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Payment"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Contact"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:S.map(e=>{var t;return(0,a.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.student_name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Father: ",e.father_name]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Mother: ",e.mother_name]}),e.pendingReason&&(0,a.jsx)("div",{className:"text-xs text-red-600 mt-1 bg-red-50 px-2 py-1 rounded",children:e.pendingReason})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.class?"".concat(e.class.name," ").concat(e.class.section):"N/A"}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:["₹",e.totalPaid.toLocaleString()]}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("span",{className:"text-sm font-medium text-red-600",children:["₹",e.totalPending.toLocaleString()]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.lastPaymentDate?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{children:["₹",null==(t=e.lastPaymentAmount)?void 0:t.toLocaleString()]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:(0,u.GP)(new Date(e.lastPaymentDate),"dd/MM/yyyy")})]}):(0,a.jsx)("span",{className:"text-gray-400",children:"No payments"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:(0,a.jsxs)("div",{children:[e.father_mobile&&(0,a.jsxs)("div",{className:"text-xs",children:["F: ",e.father_mobile]}),e.mother_mobile&&(0,a.jsxs)("div",{className:"text-xs",children:["M: ",e.mother_mobile]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>C(e.id),className:"text-blue-600 hover:text-blue-900 flex items-center gap-1",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),"Collect"]}),(e.father_mobile||e.mother_mobile)&&(0,a.jsxs)("button",{onClick:()=>k(e),className:"text-green-600 hover:text-green-900 flex items-center gap-1",children:[(0,a.jsx)(z.A,{className:"w-4 h-4"}),"Remind"]})]})})]},e.id)})})]})})}),x&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Quick Fee Collection"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"This will redirect you to the fee collection form with the student pre-selected."}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsx)("button",{onClick:()=>{h(null)},className:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700",children:"Go to Collection"}),(0,a.jsx)("button",{onClick:()=>h(null),className:"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400",children:"Cancel"})]})]})})]})]})})}var B=s(40133),V=s(17580),J=s(40646),q=s(54861),K=s(81497),Y=s(12486),W=s(54239);function X(){let[e,t]=(0,l.useState)(new Date),[s,n]=(0,l.useState)([]),[d,i]=(0,l.useState)([]),[c,o]=(0,l.useState)(!1),[m,x]=(0,l.useState)(!1),[h,g]=(0,l.useState)(null),[p,y]=(0,l.useState)(""),[b,j]=(0,l.useState)(!1),[f,N]=(0,l.useState)([]),[v,w]=(0,l.useState)("all"),[S,C]=(0,l.useState)(""),[k,A]=(0,l.useState)("all"),[P,F]=(0,l.useState)(null),[D,E]=(0,l.useState)(new Set);(0,l.useEffect)(()=>{L()},[]),(0,l.useEffect)(()=>{R()},[e,v]),(0,l.useEffect)(()=>{T()},[s,S,k]);let L=async()=>{try{let e=await fetch("/api/classes-with-names");if(!e.ok)throw Error("Failed to fetch classes");let t=await e.json();N(t)}catch(e){console.error("Error loading classes:",e)}},R=async()=>{o(!0);try{let t=(0,u.GP)(e,"yyyy-MM-dd"),s="/api/attendance/students?date=".concat(t);"all"!==v&&(s+="&class=".concat(v));let a=await fetch(s);if(!a.ok)throw Error("Failed to fetch students");let l=await a.json();n(l);let r=await fetch("/api/attendance/statistics?date=".concat(t).concat("all"!==v?"&class=".concat(v):""));if(r.ok){let e=await r.json();g(e)}}catch(e){console.error("Error loading students:",e)}finally{o(!1)}},T=()=>{let e=s;S&&(e=e.filter(e=>e.student_name.toLowerCase().includes(S.toLowerCase())||e.father_name.toLowerCase().includes(S.toLowerCase())||e.mother_name.toLowerCase().includes(S.toLowerCase()))),"all"!==k&&(e=e.filter(e=>{var t;return"unmarked"===k?!e.attendance:(null==(t=e.attendance)?void 0:t.status)===k})),i(e)},U=(t,s)=>{n(a=>a.map(a=>{if(a.id===t){var l,n,r;return{...a,attendance:{...a.attendance,student_id:t,attendance_date:(0,u.GP)(e,"yyyy-MM-dd"),status:s,marked_by:"admin",id:(null==(l=a.attendance)?void 0:l.id)||"",created_at:(null==(n=a.attendance)?void 0:n.created_at)||"",updated_at:(null==(r=a.attendance)?void 0:r.updated_at)||""}}}return a}))},G=(e,t)=>{E(s=>{let a=new Set(s);return t?a.add(e):a.delete(e),a})},z=e=>{e?E(new Set(d.map(e=>e.id))):E(new Set)},O=t=>{n(s=>s.map(s=>{if(D.has(s.id)){var a,l,n;return{...s,attendance:{...s.attendance,student_id:s.id,attendance_date:(0,u.GP)(e,"yyyy-MM-dd"),status:t,marked_by:"admin",id:(null==(a=s.attendance)?void 0:a.id)||"",created_at:(null==(l=s.attendance)?void 0:l.created_at)||"",updated_at:(null==(n=s.attendance)?void 0:n.updated_at)||""}}}return s})),E(new Set)},X=async()=>{x(!0);try{let t=s.map(t=>{var s;return{student_id:t.id,attendance_date:(0,u.GP)(e,"yyyy-MM-dd"),status:(null==(s=t.attendance)?void 0:s.status)||"absent",marked_by:"admin"}});if(!(await fetch("/api/attendance/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({attendanceList:t})})).ok)throw Error("Failed to save attendance");alert("Attendance saved successfully!"),await R()}catch(e){console.error("Error saving attendance:",e),alert("Failed to save attendance")}finally{x(!1)}},Z=async()=>{if(!p.trim())return void alert("Please enter a message to send");j(!0);try{let t=s.filter(e=>{var t;return(null==(t=e.attendance)?void 0:t.status)==="absent"});if(0===t.length)return void alert("No absent students to send messages to");let a=[];for(let s of t)s.father_mobile&&a.push({student_id:s.id,attendance_date:(0,u.GP)(e,"yyyy-MM-dd"),message_content:p,recipient_type:"father",recipient_number:s.father_mobile}),s.mother_mobile&&s.mother_mobile!==s.father_mobile&&a.push({student_id:s.id,attendance_date:(0,u.GP)(e,"yyyy-MM-dd"),message_content:p,recipient_type:"mother",recipient_number:s.mother_mobile});for(let e of a)await fetch("/api/attendance/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});alert("Messages queued for ".concat(t.length," absent students")),y("")}catch(e){console.error("Error sending messages:",e),alert("Failed to send messages")}finally{j(!1)}},Q=s.filter(e=>{var t;return(null==(t=e.attendance)?void 0:t.status)==="present"}).length,H=s.filter(e=>{var t;return(null==(t=e.attendance)?void 0:t.status)==="absent"}).length,$=s.filter(e=>{var t;return!(null==(t=e.attendance)?void 0:t.status)}).length;return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 p-4 lg:p-6",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4 lg:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl lg:text-2xl font-semibold text-gray-900 mb-2",children:"Attendance Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Mark attendance for students"})]}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-3",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"w-5 h-5 text-gray-500"}),(0,a.jsx)(W.Ay,{selected:e,onChange:e=>e&&t(e),dateFormat:"yyyy-MM-dd",className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",maxDate:new Date})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Class"}),(0,a.jsxs)("select",{value:v,onChange:e=>w(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Classes"}),f.map(e=>(0,a.jsxs)("option",{value:e.id,children:[e.name," - ",e.section]},e.id))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Search Students"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(I.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name...",value:S,onChange:e=>C(e.target.value),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Filter by Status"}),(0,a.jsxs)("select",{value:k,onChange:e=>A(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",children:[(0,a.jsx)("option",{value:"all",children:"All Students"}),(0,a.jsx)("option",{value:"present",children:"Present"}),(0,a.jsx)("option",{value:"absent",children:"Absent"}),(0,a.jsx)("option",{value:"unmarked",children:"Unmarked"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)("button",{onClick:()=>{n(t=>t.map(t=>{var s,a,l;return{...t,attendance:{...t.attendance,student_id:t.id,attendance_date:(0,u.GP)(e,"yyyy-MM-dd"),status:"present",marked_by:"admin",id:(null==(s=t.attendance)?void 0:s.id)||"",created_at:(null==(a=t.attendance)?void 0:a.created_at)||"",updated_at:(null==(l=t.attendance)?void 0:l.updated_at)||""}}}))},className:"flex-1 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm",disabled:c,children:(0,a.jsx)(r.A,{className:"w-4 h-4 mx-auto"})}),(0,a.jsx)("button",{onClick:()=>{n(e=>e.map(e=>({...e,attendance:void 0})))},className:"flex-1 px-3 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors text-sm",disabled:c,children:(0,a.jsx)(B.A,{className:"w-4 h-4 mx-auto"})})]})]})]})]})}),h&&(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(V.A,{className:"w-6 h-6 lg:w-8 lg:h-8 text-blue-500"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-xs lg:text-sm font-medium text-gray-500",children:"Total Students"}),(0,a.jsx)("p",{className:"text-lg lg:text-2xl font-semibold text-gray-900",children:h.totalStudents})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(J.A,{className:"w-6 h-6 lg:w-8 lg:h-8 text-green-500"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-xs lg:text-sm font-medium text-gray-500",children:"Present"}),(0,a.jsx)("p",{className:"text-lg lg:text-2xl font-semibold text-gray-900",children:Q})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(q.A,{className:"w-6 h-6 lg:w-8 lg:h-8 text-red-500"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-xs lg:text-sm font-medium text-gray-500",children:"Absent"}),(0,a.jsx)("p",{className:"text-lg lg:text-2xl font-semibold text-gray-900",children:H})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(_.A,{className:"w-6 h-6 lg:w-8 lg:h-8 text-yellow-500"}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("p",{className:"text-xs lg:text-sm font-medium text-gray-500",children:"Unmarked"}),(0,a.jsx)("p",{className:"text-lg lg:text-2xl font-semibold text-gray-900",children:$})]})]})})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-4 lg:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Actions"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:D.size>0?"".concat(D.size," students selected"):$>0?"".concat($," students not marked yet"):"All students marked"})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[D.size>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("button",{onClick:()=>O("present"),className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-sm",children:"Mark Selected Present"}),(0,a.jsx)("button",{onClick:()=>O("absent"),className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors text-sm",children:"Mark Selected Absent"})]}),(0,a.jsxs)("button",{onClick:X,disabled:m,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2 text-sm",children:[(0,a.jsx)(M.A,{className:"w-4 h-4"}),m?"Saving...":"Save Attendance"]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border",children:[(0,a.jsx)("div",{className:"p-4 lg:p-6 border-b",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Students"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[d.length," of ",s.length," students","all"!==v&&" in selected class"]})]}),d.length>0&&(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.size===d.length&&d.length>0,onChange:e=>z(e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Select All"})]})]})}),(0,a.jsx)("div",{className:"p-4 lg:p-6",children:c?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading students..."})]}):0===d.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(V.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:S||"all"!==k?"No students match your filters":"No students found"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4",children:d.map(e=>{var t,s,l,n,r,d,i,c;return(0,a.jsxs)("div",{className:"border rounded-lg p-4 transition-all hover:shadow-md ".concat((null==(t=e.attendance)?void 0:t.status)==="present"?"border-green-200 bg-green-50":(null==(s=e.attendance)?void 0:s.status)==="absent"?"border-red-200 bg-red-50":"border-gray-200 bg-white hover:bg-gray-50"),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-start gap-3",children:[(0,a.jsx)("input",{type:"checkbox",checked:D.has(e.id),onChange:t=>G(e.id,t.target.checked),className:"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 text-sm lg:text-base",children:e.student_name}),(0,a.jsxs)("p",{className:"text-xs lg:text-sm text-gray-600",children:["Class: ",(null==(l=e.class)?void 0:l.name)?"".concat(e.class.name," - ").concat(e.class.section):e.class_id]}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["Father: ",e.father_name]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(null==(n=e.attendance)?void 0:n.status)==="present"&&(0,a.jsx)(J.A,{className:"w-5 h-5 text-green-500"}),(null==(r=e.attendance)?void 0:r.status)==="absent"&&(0,a.jsx)(q.A,{className:"w-5 h-5 text-red-500"})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("button",{onClick:()=>U(e.id,"present"),className:"flex-1 px-3 py-2 rounded-md text-xs lg:text-sm font-medium transition-colors ".concat((null==(d=e.attendance)?void 0:d.status)==="present"?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-green-100 hover:text-green-700"),children:[(0,a.jsx)(J.A,{className:"w-4 h-4 mx-auto lg:hidden"}),(0,a.jsx)("span",{className:"hidden lg:inline",children:"Present"})]}),(0,a.jsxs)("button",{onClick:()=>U(e.id,"absent"),className:"flex-1 px-3 py-2 rounded-md text-xs lg:text-sm font-medium transition-colors ".concat((null==(i=e.attendance)?void 0:i.status)==="absent"?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-red-100 hover:text-red-700"),children:[(0,a.jsx)(q.A,{className:"w-4 h-4 mx-auto lg:hidden"}),(0,a.jsx)("span",{className:"hidden lg:inline",children:"Absent"})]})]}),(null==(c=e.attendance)?void 0:c.status)==="absent"&&(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,a.jsxs)("p",{children:["\uD83D\uDCDE Father: ",e.father_mobile||"N/A"]}),(0,a.jsxs)("p",{children:["\uD83D\uDCDE Mother: ",e.mother_mobile||"N/A"]})]})})]},e.id)})})})]}),H>0&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-4 lg:p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(K.A,{className:"w-5 h-5 text-blue-500"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:"Send Message to Absent Students"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:"Message Content"}),(0,a.jsx)("textarea",{id:"message",value:p,onChange:e=>y(e.target.value),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm",placeholder:"Enter message to send to parents of absent students..."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Will send to ",H," absent students' parents"]}),(0,a.jsxs)("button",{onClick:Z,disabled:b||!p.trim(),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2 text-sm",children:[(0,a.jsx)(Y.A,{className:"w-4 h-4"}),b?"Sending...":"Send Messages"]})]})]})]})]})})}s(35279);var Z=s(33109),Q=s(84355),H=s(83540),$=s(93504),ee=s(94754),et=s(96025),es=s(52071),ea=s(85755),el=s(64683),en=s(74584),er=s(90170),ed=s(10627),ei=s(54811),ec=s(99445),eo=s(61667);function em(){let[e,t]=(0,l.useState)(null),[s,r]=(0,l.useState)(null),[d,i]=(0,l.useState)([]),[c,o]=(0,l.useState)(!0),[m,x]=(0,l.useState)(new Date);(0,l.useEffect)(()=>{h()},[m]);let h=async()=>{o(!0);try{let e=(0,u.GP)(m,"yyyy-MM-dd"),s=await fetch("/api/attendance/statistics?date=".concat(e));if(s.ok){let e=await s.json();t(e)}let a=await fetch("/api/pending-fees");if(a.ok){let e=await a.json(),t=e.reduce((e,t)=>e+(t.totalPaid||0),0),s=e.reduce((e,t)=>e+(t.totalPending||0),0);r({totalCollected:t,totalPending:s,totalStudents:e.length,studentsWithPending:e.filter(e=>e.totalPending>0).length})}let l=await fetch("/api/attendance/trends?days=30");if(l.ok){let e=await l.json();i(e)}}catch(e){console.error("Error loading dashboard data:",e)}finally{o(!1)}},g=e?[{name:"Present",value:e.presentCount,color:"#10B981"},{name:"Absent",value:e.absentCount,color:"#EF4444"}]:[],p=s?[{name:"Collected",value:s.totalCollected,color:"#10B981"},{name:"Pending",value:s.totalPending,color:"#F59E0B"}]:[];return c?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-96",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"First Step School Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Saurabh Vihar, Jaitpur, Delhi"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(_.A,{className:"w-5 h-5 text-gray-500"}),(0,a.jsx)("input",{type:"date",value:(0,u.GP)(m,"yyyy-MM-dd"),onChange:e=>x(new Date(e.target.value)),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",max:(0,u.GP)(new Date,"yyyy-MM-dd")})]})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,a.jsx)(V.A,{className:"w-6 h-6 text-blue-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Students"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==e?void 0:e.totalStudents)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,a.jsx)(J.A,{className:"w-6 h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Present Today"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==e?void 0:e.presentCount)||0}),(0,a.jsxs)("p",{className:"text-xs text-green-600",children:[null==e?void 0:e.attendancePercentage.toFixed(1),"% attendance"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,a.jsx)(q.A,{className:"w-6 h-6 text-red-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Absent Today"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==e?void 0:e.absentCount)||0})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-2 bg-yellow-100 rounded-lg",children:(0,a.jsx)(w.A,{className:"w-6 h-6 text-yellow-600"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Fees Collected"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["₹",(null==s?void 0:s.totalCollected.toLocaleString())||0]}),(0,a.jsxs)("p",{className:"text-xs text-yellow-600",children:["₹",(null==s?void 0:s.totalPending.toLocaleString())||0," pending"]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(Z.A,{className:"w-5 h-5 text-blue-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Attendance Trends (30 Days)"})]}),(0,a.jsx)(H.u,{width:"100%",height:300,children:(0,a.jsxs)($.b,{data:d,children:[(0,a.jsx)(ee.d,{strokeDasharray:"3 3"}),(0,a.jsx)(et.W,{dataKey:"date",tickFormatter:e=>(0,u.GP)(new Date(e),"MM/dd")}),(0,a.jsx)(es.h,{}),(0,a.jsx)(ea.m,{labelFormatter:e=>(0,u.GP)(new Date(e),"MMM dd, yyyy")}),(0,a.jsx)(el.s,{}),(0,a.jsx)(en.N,{type:"monotone",dataKey:"presentCount",stroke:"#10B981",strokeWidth:2,name:"Present"}),(0,a.jsx)(en.N,{type:"monotone",dataKey:"absentCount",stroke:"#EF4444",strokeWidth:2,name:"Absent"})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(Q.A,{className:"w-5 h-5 text-green-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Today's Attendance"})]}),(0,a.jsx)(H.u,{width:"100%",height:300,children:(0,a.jsxs)(er.r,{children:[(0,a.jsx)(ed.F,{data:g,cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:"value",children:g.map((e,t)=>(0,a.jsx)(ei.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(ea.m,{}),(0,a.jsx)(el.s,{})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 text-yellow-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Fee Collection Overview"})]}),(0,a.jsx)(H.u,{width:"100%",height:300,children:(0,a.jsxs)(er.r,{children:[(0,a.jsx)(ed.F,{data:p,cx:"50%",cy:"50%",innerRadius:60,outerRadius:100,paddingAngle:5,dataKey:"value",children:p.map((e,t)=>(0,a.jsx)(ei.f,{fill:e.color},"cell-".concat(t)))}),(0,a.jsx)(ea.m,{formatter:e=>"₹".concat(e.toLocaleString())}),(0,a.jsx)(el.s,{})]})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,a.jsx)(Z.A,{className:"w-5 h-5 text-purple-500"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Attendance Percentage Trend"})]}),(0,a.jsx)(H.u,{width:"100%",height:300,children:(0,a.jsxs)(ec.Q,{data:d,children:[(0,a.jsx)(ee.d,{strokeDasharray:"3 3"}),(0,a.jsx)(et.W,{dataKey:"date",tickFormatter:e=>(0,u.GP)(new Date(e),"MM/dd")}),(0,a.jsx)(es.h,{domain:[0,100]}),(0,a.jsx)(ea.m,{labelFormatter:e=>(0,u.GP)(new Date(e),"MMM dd, yyyy"),formatter:e=>["".concat(e,"%"),"Attendance"]}),(0,a.jsx)(eo.Gk,{type:"monotone",dataKey:"attendancePercentage",stroke:"#8B5CF6",fill:"#8B5CF6",fillOpacity:.3})]})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Attendance Summary"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Students"}),(0,a.jsx)("span",{className:"font-semibold",children:(null==e?void 0:e.totalStudents)||0})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Present Today"}),(0,a.jsx)("span",{className:"font-semibold text-green-600",children:(null==e?void 0:e.presentCount)||0})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Absent Today"}),(0,a.jsx)("span",{className:"font-semibold text-red-600",children:(null==e?void 0:e.absentCount)||0})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center pt-2 border-t",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Attendance Rate"}),(0,a.jsxs)("span",{className:"font-semibold text-blue-600",children:[null==e?void 0:e.attendancePercentage.toFixed(1),"%"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Fee Collection Summary"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Total Students"}),(0,a.jsx)("span",{className:"font-semibold",children:(null==s?void 0:s.totalStudents)||0})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Fees Collected"}),(0,a.jsxs)("span",{className:"font-semibold text-green-600",children:["₹",(null==s?void 0:s.totalCollected.toLocaleString())||0]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Pending Fees"}),(0,a.jsxs)("span",{className:"font-semibold text-yellow-600",children:["₹",(null==s?void 0:s.totalPending.toLocaleString())||0]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center pt-2 border-t",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Students with Pending"}),(0,a.jsx)("span",{className:"font-semibold text-red-600",children:(null==s?void 0:s.studentsWithPending)||0})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,a.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,a.jsx)(V.A,{className:"w-6 h-6 text-blue-500 mb-2"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Mark Attendance"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Record today's attendance"})]}),(0,a.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,a.jsx)(w.A,{className:"w-6 h-6 text-green-500 mb-2"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Collect Fees"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Record fee payments"})]}),(0,a.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,a.jsx)(n.A,{className:"w-6 h-6 text-purple-500 mb-2"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"View Reports"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Generate detailed reports"})]}),(0,a.jsxs)("button",{className:"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left",children:[(0,a.jsx)(_.A,{className:"w-6 h-6 text-orange-500 mb-2"}),(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"View Calendar"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Check school calendar"})]})]})]})]})}function ex(){var e,t;let[s,o]=(0,l.useState)("dashboard"),m=[{id:"dashboard",name:"Dashboard",icon:n.A,description:"Overview of attendance and fees"},{id:"attendance",name:"Attendance",icon:r.A,description:"Mark and manage student attendance"},{id:"collection",name:"Fee Collection",icon:d.A,description:"Record new fee payments"},{id:"records",name:"Fee Records",icon:i.A,description:"View all fee submission records"},{id:"pending",name:"Pending Fees",icon:c.A,description:"View students with pending fees"}];return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("div",{className:"py-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"First Step School"}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:"Saurabh Vihar, Jaitpur, Delhi"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"School Management System"})]})})})}),(0,a.jsx)("div",{className:"bg-white shadow-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("nav",{className:"flex space-x-8","aria-label":"Tabs",children:m.map(e=>{let t=e.icon,l=s===e.id;return(0,a.jsxs)("button",{onClick:()=>o(e.id),className:"".concat(l?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"," whitespace-nowrap py-4 px-6 border-b-2 font-medium text-sm flex items-center gap-2 transition-colors duration-200 rounded-t-lg"),"aria-current":l?"page":void 0,children:[(0,a.jsx)(t,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"hidden sm:inline",children:e.name}),(0,a.jsx)("span",{className:"sm:hidden",children:e.name.split(" ")[0]})]},e.id)})})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:null==(e=m.find(e=>e.id===s))?void 0:e.name}),(0,a.jsx)("p",{className:"text-gray-600",children:null==(t=m.find(e=>e.id===s))?void 0:t.description})]}),(()=>{switch(s){case"dashboard":default:return(0,a.jsx)(em,{});case"attendance":return(0,a.jsx)(X,{});case"collection":return(0,a.jsx)(k,{});case"records":return(0,a.jsx)(U,{});case"pending":return(0,a.jsx)(O,{})}})()]})})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[302,545,579,258,722,441,684,358],()=>t(8587)),_N_E=e.O()}]);