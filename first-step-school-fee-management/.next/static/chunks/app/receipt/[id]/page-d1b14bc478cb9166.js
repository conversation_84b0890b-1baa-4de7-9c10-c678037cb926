(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[685],{14861:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});var a=t(95155),l=t(26681);let n=(0,t(19946).A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]);var r=t(66516);function i(e){let{payment:s}=e;return(0,a.jsxs)("div",{className:"bg-white min-h-screen lg:min-h-0",children:[(0,a.jsxs)("div",{className:"print:hidden mb-4 lg:mb-6 flex flex-col sm:flex-row justify-end gap-3",children:[(0,a.jsxs)("button",{onClick:()=>{window.print()},className:"flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm lg:text-base",children:[(0,a.jsx)(n,{className:"w-4 h-4"}),"Print Receipt"]}),(0,a.jsxs)("button",{onClick:()=>{navigator.share?navigator.share({title:"Fee Receipt - ".concat(s.student.student_name),text:"Fee payment receipt for ".concat(s.student.student_name),url:window.location.href}):(navigator.clipboard.writeText(window.location.href),alert("Receipt URL copied to clipboard!"))},className:"flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm lg:text-base",children:[(0,a.jsx)(r.A,{className:"w-4 h-4"}),"Share"]})]}),(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 lg:p-8 print:shadow-none print:border-none",children:[(0,a.jsxs)("div",{className:"text-center border-b border-gray-200 pb-4 lg:pb-6 mb-4 lg:mb-6",children:[(0,a.jsx)("h1",{className:"text-xl lg:text-3xl font-bold text-gray-900 mb-2",children:"First Step School"}),(0,a.jsx)("p",{className:"text-sm lg:text-base text-gray-600 mb-1",children:"Your School Address Here"}),(0,a.jsxs)("p",{className:"text-sm lg:text-base text-gray-600 mb-4",children:["Website: ","www.firststepschool.com"]}),(0,a.jsx)("h2",{className:"text-lg lg:text-xl font-semibold text-gray-800",children:"FEE PAYMENT RECEIPT"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 mb-4 lg:mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-3",children:"Receipt Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-xs lg:text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Receipt ID:"}),(0,a.jsx)("span",{className:"font-medium",children:s.id.slice(-8).toUpperCase()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Payment Date:"}),(0,a.jsx)("span",{className:"font-medium",children:(0,l.GP)(new Date(s.payment_date),"dd MMM yyyy")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Generated On:"}),(0,a.jsx)("span",{className:"font-medium",children:(0,l.GP)(new Date(s.created_at),"dd MMM yyyy, hh:mm a")})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-3",children:"Payment Status"}),(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)("span",{className:"inline-block px-3 py-1 rounded-full text-xs lg:text-sm font-medium ".concat((e=>{switch(e){case"completed":return"text-green-600 bg-green-100";case"partial":return"text-yellow-600 bg-yellow-100";case"pending":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(s.payment_status)),children:s.payment_status.charAt(0).toUpperCase()+s.payment_status.slice(1)})})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 lg:p-6 mb-4 lg:mb-6",children:[(0,a.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-4",children:"Student Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2 text-xs lg:text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Student Name:"}),(0,a.jsx)("span",{className:"font-medium break-words",children:s.student.student_name})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Class:"}),(0,a.jsx)("span",{className:"font-medium",children:s.student.class?"".concat(s.student.class.name," - ").concat(s.student.class.section):s.student.class_id})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Father's Name:"}),(0,a.jsx)("span",{className:"font-medium break-words",children:s.student.father_name})]})]}),(0,a.jsxs)("div",{className:"space-y-2 text-xs lg:text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Mother's Name:"}),(0,a.jsx)("span",{className:"font-medium break-words",children:s.student.mother_name})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Father's Mobile:"}),(0,a.jsx)("span",{className:"font-medium",children:s.student.father_mobile||"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Mother's Mobile:"}),(0,a.jsx)("span",{className:"font-medium",children:s.student.mother_mobile||"N/A"})]})]})]})]}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 lg:p-6 mb-4 lg:mb-6",children:[(0,a.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-4",children:"Payment Details"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-gray-600 text-sm lg:text-base",children:"Amount Received:"}),(0,a.jsxs)("span",{className:"text-lg lg:text-2xl font-bold text-green-600",children:["₹",s.amount_received.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-gray-600 text-sm lg:text-base",children:"Payment Method:"}),(0,a.jsx)("span",{className:"font-medium text-sm lg:text-base",children:(e=>{switch(e){case"bank_transfer":return"Bank Transfer";case"upi":return"UPI";default:return e.charAt(0).toUpperCase()+e.slice(1)}})(s.payment_method)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-gray-600 text-sm lg:text-base",children:"Balance Remaining:"}),(0,a.jsxs)("span",{className:"font-medium text-sm lg:text-base ".concat(s.balance_remaining>0?"text-red-600":"text-green-600"),children:["₹",s.balance_remaining.toFixed(2)]})]})]})]}),s.notes&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-4 lg:mb-6",children:[(0,a.jsx)("h3",{className:"text-base lg:text-lg font-semibold text-gray-800 mb-2",children:"Additional Notes"}),(0,a.jsx)("p",{className:"text-gray-700 text-sm lg:text-base break-words",children:s.notes})]}),(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4 lg:pt-6 text-center",children:[(0,a.jsx)("p",{className:"text-xs lg:text-sm text-gray-600 mb-2",children:"This is a computer-generated receipt and does not require a signature."}),(0,a.jsx)("p",{className:"text-xs lg:text-sm text-gray-600",children:"For any queries, please contact the school office."}),(0,a.jsxs)("div",{className:"mt-4 text-xs text-gray-500 break-all",children:["Receipt URL: ",window.location.href]})]})]})]})}},67953:(e,s,t)=>{Promise.resolve().then(t.bind(t,14861))}},e=>{var s=s=>e(e.s=s);e.O(0,[258,441,684,358],()=>s(67953)),_N_E=e.O()}]);