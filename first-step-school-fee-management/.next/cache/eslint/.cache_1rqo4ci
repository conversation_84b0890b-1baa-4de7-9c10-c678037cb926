[{"/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/bulk/route.ts": "1", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/messages/route.ts": "2", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/route.ts": "3", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/statistics/route.ts": "4", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/students/route.ts": "5", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/trends/route.ts": "6", "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes/route.ts": "7", "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes-with-names/route.ts": "8", "/home/<USER>/school/first-step-school-fee-management/src/app/api/fee-history/route.ts": "9", "/home/<USER>/school/first-step-school-fee-management/src/app/api/payments/route.ts": "10", "/home/<USER>/school/first-step-school-fee-management/src/app/api/pending-fees/route.ts": "11", "/home/<USER>/school/first-step-school-fee-management/src/app/api/students/route.ts": "12", "/home/<USER>/school/first-step-school-fee-management/src/app/api/test-pending/route.ts": "13", "/home/<USER>/school/first-step-school-fee-management/src/app/layout.tsx": "14", "/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx": "15", "/home/<USER>/school/first-step-school-fee-management/src/app/page.tsx": "16", "/home/<USER>/school/first-step-school-fee-management/src/app/receipt/[id]/page.tsx": "17", "/home/<USER>/school/first-step-school-fee-management/src/components/AttendanceManagement.tsx": "18", "/home/<USER>/school/first-step-school-fee-management/src/components/DashboardAnalytics.tsx": "19", "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementDashboard.tsx": "20", "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementForm.tsx": "21", "/home/<USER>/school/first-step-school-fee-management/src/components/FeeRecordsComponent.tsx": "22", "/home/<USER>/school/first-step-school-fee-management/src/components/PendingFeesComponent.tsx": "23", "/home/<USER>/school/first-step-school-fee-management/src/components/ReceiptComponent.tsx": "24", "/home/<USER>/school/first-step-school-fee-management/src/lib/database.ts": "25", "/home/<USER>/school/first-step-school-fee-management/src/lib/supabase.ts": "26", "/home/<USER>/school/first-step-school-fee-management/src/types/database.ts": "27"}, {"size": 1639, "mtime": 1751280746396, "results": "28", "hashOfConfig": "29"}, {"size": 1914, "mtime": 1751280781165, "results": "30", "hashOfConfig": "29"}, {"size": 1824, "mtime": 1751280736812, "results": "31", "hashOfConfig": "29"}, {"size": 765, "mtime": 1751283764999, "results": "32", "hashOfConfig": "29"}, {"size": 813, "mtime": 1751283727515, "results": "33", "hashOfConfig": "29"}, {"size": 782, "mtime": 1751280769753, "results": "34", "hashOfConfig": "29"}, {"size": 635, "mtime": 1751271543424, "results": "35", "hashOfConfig": "29"}, {"size": 538, "mtime": 1751272032013, "results": "36", "hashOfConfig": "29"}, {"size": 882, "mtime": 1751276621194, "results": "37", "hashOfConfig": "29"}, {"size": 6726, "mtime": 1751276607946, "results": "38", "hashOfConfig": "29"}, {"size": 4233, "mtime": 1751283262126, "results": "39", "hashOfConfig": "29"}, {"size": 799, "mtime": 1751271560412, "results": "40", "hashOfConfig": "29"}, {"size": 4122, "mtime": 1751283287998, "results": "41", "hashOfConfig": "29"}, {"size": 607, "mtime": 1751281108723, "results": "42", "hashOfConfig": "29"}, {"size": 744, "mtime": 1751281617009, "results": "43", "hashOfConfig": "29"}, {"size": 215, "mtime": 1751272539467, "results": "44", "hashOfConfig": "29"}, {"size": 1623, "mtime": 1751283334510, "results": "45", "hashOfConfig": "29"}, {"size": 26232, "mtime": 1751283713699, "results": "46", "hashOfConfig": "29"}, {"size": 16478, "mtime": 1751281421205, "results": "47", "hashOfConfig": "29"}, {"size": 4407, "mtime": 1751281052075, "results": "48", "hashOfConfig": "29"}, {"size": 12957, "mtime": 1751272861234, "results": "49", "hashOfConfig": "29"}, {"size": 27610, "mtime": 1751276903004, "results": "50", "hashOfConfig": "29"}, {"size": 15726, "mtime": 1751284051053, "results": "51", "hashOfConfig": "29"}, {"size": 8998, "mtime": 1751283904786, "results": "52", "hashOfConfig": "29"}, {"size": 18962, "mtime": 1751283788139, "results": "53", "hashOfConfig": "29"}, {"size": 753, "mtime": 1751271526940, "results": "54", "hashOfConfig": "29"}, {"size": 2762, "mtime": 1751280585428, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1187ui9", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/bulk/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/messages/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/statistics/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/students/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/trends/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes-with-names/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/fee-history/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/payments/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/pending-fees/route.ts", ["137"], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/students/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/test-pending/route.ts", ["138", "139", "140", "141"], [], "/home/<USER>/school/first-step-school-fee-management/src/app/layout.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/page.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/receipt/[id]/page.tsx", ["142", "143"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/AttendanceManagement.tsx", ["144", "145", "146", "147", "148", "149", "150", "151"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/DashboardAnalytics.tsx", ["152", "153", "154", "155", "156", "157", "158", "159", "160"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementDashboard.tsx", ["161"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementForm.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/components/FeeRecordsComponent.tsx", ["162", "163", "164", "165"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/PendingFeesComponent.tsx", ["166", "167", "168"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/ReceiptComponent.tsx", ["169", "170", "171", "172", "173"], [], "/home/<USER>/school/first-step-school-fee-management/src/lib/database.ts", ["174", "175", "176", "177", "178", "179"], [], "/home/<USER>/school/first-step-school-fee-management/src/lib/supabase.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/types/database.ts", [], [], {"ruleId": "180", "severity": 2, "message": "181", "line": 85, "column": 59, "nodeType": "182", "messageId": "183", "endLine": 85, "endColumn": 62, "suggestions": "184"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 73, "column": 61, "nodeType": "182", "messageId": "183", "endLine": 73, "endColumn": 64, "suggestions": "185"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 80, "column": 73, "nodeType": "182", "messageId": "183", "endLine": 80, "endColumn": 76, "suggestions": "186"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 84, "column": 54, "nodeType": "182", "messageId": "183", "endLine": 84, "endColumn": 57, "suggestions": "187"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 84, "column": 62, "nodeType": "182", "messageId": "183", "endLine": 84, "endColumn": 65, "suggestions": "188"}, {"ruleId": "189", "severity": 2, "message": "190", "line": 2, "column": 10, "nodeType": null, "messageId": "191", "endLine": 2, "endColumn": 16}, {"ruleId": "189", "severity": 2, "message": "192", "line": 55, "column": 12, "nodeType": null, "messageId": "191", "endLine": 55, "endColumn": 17}, {"ruleId": "189", "severity": 2, "message": "193", "line": 5, "column": 76, "nodeType": null, "messageId": "191", "endLine": 5, "endColumn": 82}, {"ruleId": "189", "severity": 2, "message": "194", "line": 5, "column": 114, "nodeType": null, "messageId": "191", "endLine": 5, "endColumn": 119}, {"ruleId": "189", "severity": 2, "message": "195", "line": 42, "column": 10, "nodeType": null, "messageId": "191", "endLine": 42, "endColumn": 20}, {"ruleId": "189", "severity": 2, "message": "196", "line": 42, "column": 22, "nodeType": null, "messageId": "191", "endLine": 42, "endColumn": 35}, {"ruleId": "197", "severity": 1, "message": "198", "line": 53, "column": 6, "nodeType": "199", "endLine": 53, "endColumn": 35, "suggestions": "200"}, {"ruleId": "197", "severity": 1, "message": "201", "line": 58, "column": 6, "nodeType": "199", "endLine": 58, "endColumn": 46, "suggestions": "202"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 370, "column": 74, "nodeType": "182", "messageId": "183", "endLine": 370, "endColumn": 77, "suggestions": "203"}, {"ruleId": "204", "severity": 2, "message": "205", "line": 639, "column": 61, "nodeType": "206", "messageId": "207", "suggestions": "208"}, {"ruleId": "189", "severity": 2, "message": "209", "line": 20, "column": 3, "nodeType": null, "messageId": "191", "endLine": 20, "endColumn": 11}, {"ruleId": "189", "severity": 2, "message": "210", "line": 21, "column": 3, "nodeType": null, "messageId": "191", "endLine": 21, "endColumn": 6}, {"ruleId": "189", "severity": 2, "message": "211", "line": 54, "column": 7, "nodeType": null, "messageId": "191", "endLine": 54, "endColumn": 13}, {"ruleId": "197", "severity": 1, "message": "212", "line": 65, "column": 6, "nodeType": "199", "endLine": 65, "endColumn": 20, "suggestions": "213"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 84, "column": 70, "nodeType": "182", "messageId": "183", "endLine": 84, "endColumn": 73, "suggestions": "214"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 85, "column": 68, "nodeType": "182", "messageId": "183", "endLine": 85, "endColumn": 71, "suggestions": "215"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 91, "column": 57, "nodeType": "182", "messageId": "183", "endLine": 91, "endColumn": 60, "suggestions": "216"}, {"ruleId": "204", "severity": 2, "message": "205", "line": 263, "column": 70, "nodeType": "206", "messageId": "207", "suggestions": "217"}, {"ruleId": "204", "severity": 2, "message": "205", "line": 413, "column": 62, "nodeType": "206", "messageId": "207", "suggestions": "218"}, {"ruleId": "189", "severity": 2, "message": "219", "line": 4, "column": 39, "nodeType": null, "messageId": "191", "endLine": 4, "endColumn": 44}, {"ruleId": "189", "severity": 2, "message": "220", "line": 5, "column": 10, "nodeType": null, "messageId": "191", "endLine": 5, "endColumn": 16}, {"ruleId": "197", "severity": 1, "message": "221", "line": 69, "column": 6, "nodeType": "199", "endLine": 69, "endColumn": 28, "suggestions": "222"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 377, "column": 112, "nodeType": "182", "messageId": "183", "endLine": 377, "endColumn": 115, "suggestions": "223"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 394, "column": 112, "nodeType": "182", "messageId": "183", "endLine": 394, "endColumn": 115, "suggestions": "224"}, {"ruleId": "189", "severity": 2, "message": "193", "line": 5, "column": 43, "nodeType": null, "messageId": "191", "endLine": 5, "endColumn": 49}, {"ruleId": "189", "severity": 2, "message": "225", "line": 6, "column": 10, "nodeType": null, "messageId": "191", "endLine": 6, "endColumn": 20}, {"ruleId": "197", "severity": 1, "message": "226", "line": 66, "column": 6, "nodeType": "199", "endLine": 66, "endColumn": 52, "suggestions": "227"}, {"ruleId": "189", "severity": 2, "message": "228", "line": 5, "column": 19, "nodeType": null, "messageId": "191", "endLine": 5, "endColumn": 27}, {"ruleId": "204", "severity": 2, "message": "205", "line": 138, "column": 55, "nodeType": "206", "messageId": "207", "suggestions": "229"}, {"ruleId": "204", "severity": 2, "message": "205", "line": 144, "column": 55, "nodeType": "206", "messageId": "207", "suggestions": "230"}, {"ruleId": "204", "severity": 2, "message": "205", "line": 148, "column": 55, "nodeType": "206", "messageId": "207", "suggestions": "231"}, {"ruleId": "204", "severity": 2, "message": "205", "line": 152, "column": 55, "nodeType": "206", "messageId": "207", "suggestions": "232"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 192, "column": 61, "nodeType": "182", "messageId": "183", "endLine": 192, "endColumn": 64, "suggestions": "233"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 248, "column": 71, "nodeType": "182", "messageId": "183", "endLine": 248, "endColumn": 74, "suggestions": "234"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 251, "column": 74, "nodeType": "182", "messageId": "183", "endLine": 251, "endColumn": 77, "suggestions": "235"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 255, "column": 52, "nodeType": "182", "messageId": "183", "endLine": 255, "endColumn": 55, "suggestions": "236"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 255, "column": 60, "nodeType": "182", "messageId": "183", "endLine": 255, "endColumn": 63, "suggestions": "237"}, {"ruleId": "180", "severity": 2, "message": "181", "line": 260, "column": 66, "nodeType": "182", "messageId": "183", "endLine": 260, "endColumn": 69, "suggestions": "238"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["239", "240"], ["241", "242"], ["243", "244"], ["245", "246"], ["247", "248"], "@typescript-eslint/no-unused-vars", "'format' is defined but never used.", "unusedVar", "'error' is defined but never used.", "'Filter' is defined but never used.", "'UserX' is defined but never used.", "'bulkAction' is assigned a value but never used.", "'setBulkAction' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadStudentsWithAttendance'. Either include it or remove the dependency array.", "ArrayExpression", ["249"], "React Hook useEffect has a missing dependency: 'filterStudents'. Either include it or remove the dependency array.", ["250"], ["251", "252"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["253", "254", "255", "256"], "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'COLORS' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["257"], ["258", "259"], ["260", "261"], ["262", "263"], ["264", "265", "266", "267"], ["268", "269", "270", "271"], "'Users' is defined but never used.", "'Search' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", ["272"], ["273", "274"], ["275", "276"], "'FeePayment' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPendingFees'. Either include it or remove the dependency array.", ["277"], "'Download' is defined but never used.", ["278", "279", "280", "281"], ["282", "283", "284", "285"], ["286", "287", "288", "289"], ["290", "291", "292", "293"], ["294", "295"], ["296", "297"], ["298", "299"], ["300", "301"], ["302", "303"], ["304", "305"], {"messageId": "306", "fix": "307", "desc": "308"}, {"messageId": "309", "fix": "310", "desc": "311"}, {"messageId": "306", "fix": "312", "desc": "308"}, {"messageId": "309", "fix": "313", "desc": "311"}, {"messageId": "306", "fix": "314", "desc": "308"}, {"messageId": "309", "fix": "315", "desc": "311"}, {"messageId": "306", "fix": "316", "desc": "308"}, {"messageId": "309", "fix": "317", "desc": "311"}, {"messageId": "306", "fix": "318", "desc": "308"}, {"messageId": "309", "fix": "319", "desc": "311"}, {"desc": "320", "fix": "321"}, {"desc": "322", "fix": "323"}, {"messageId": "306", "fix": "324", "desc": "308"}, {"messageId": "309", "fix": "325", "desc": "311"}, {"messageId": "326", "data": "327", "fix": "328", "desc": "329"}, {"messageId": "326", "data": "330", "fix": "331", "desc": "332"}, {"messageId": "326", "data": "333", "fix": "334", "desc": "335"}, {"messageId": "326", "data": "336", "fix": "337", "desc": "338"}, {"desc": "339", "fix": "340"}, {"messageId": "306", "fix": "341", "desc": "308"}, {"messageId": "309", "fix": "342", "desc": "311"}, {"messageId": "306", "fix": "343", "desc": "308"}, {"messageId": "309", "fix": "344", "desc": "311"}, {"messageId": "306", "fix": "345", "desc": "308"}, {"messageId": "309", "fix": "346", "desc": "311"}, {"messageId": "326", "data": "347", "fix": "348", "desc": "329"}, {"messageId": "326", "data": "349", "fix": "350", "desc": "332"}, {"messageId": "326", "data": "351", "fix": "352", "desc": "335"}, {"messageId": "326", "data": "353", "fix": "354", "desc": "338"}, {"messageId": "326", "data": "355", "fix": "356", "desc": "329"}, {"messageId": "326", "data": "357", "fix": "358", "desc": "332"}, {"messageId": "326", "data": "359", "fix": "360", "desc": "335"}, {"messageId": "326", "data": "361", "fix": "362", "desc": "338"}, {"desc": "363", "fix": "364"}, {"messageId": "306", "fix": "365", "desc": "308"}, {"messageId": "309", "fix": "366", "desc": "311"}, {"messageId": "306", "fix": "367", "desc": "308"}, {"messageId": "309", "fix": "368", "desc": "311"}, {"desc": "369", "fix": "370"}, {"messageId": "326", "data": "371", "fix": "372", "desc": "329"}, {"messageId": "326", "data": "373", "fix": "374", "desc": "332"}, {"messageId": "326", "data": "375", "fix": "376", "desc": "335"}, {"messageId": "326", "data": "377", "fix": "378", "desc": "338"}, {"messageId": "326", "data": "379", "fix": "380", "desc": "329"}, {"messageId": "326", "data": "381", "fix": "382", "desc": "332"}, {"messageId": "326", "data": "383", "fix": "384", "desc": "335"}, {"messageId": "326", "data": "385", "fix": "386", "desc": "338"}, {"messageId": "326", "data": "387", "fix": "388", "desc": "329"}, {"messageId": "326", "data": "389", "fix": "390", "desc": "332"}, {"messageId": "326", "data": "391", "fix": "392", "desc": "335"}, {"messageId": "326", "data": "393", "fix": "394", "desc": "338"}, {"messageId": "326", "data": "395", "fix": "396", "desc": "329"}, {"messageId": "326", "data": "397", "fix": "398", "desc": "332"}, {"messageId": "326", "data": "399", "fix": "400", "desc": "335"}, {"messageId": "326", "data": "401", "fix": "402", "desc": "338"}, {"messageId": "306", "fix": "403", "desc": "308"}, {"messageId": "309", "fix": "404", "desc": "311"}, {"messageId": "306", "fix": "405", "desc": "308"}, {"messageId": "309", "fix": "406", "desc": "311"}, {"messageId": "306", "fix": "407", "desc": "308"}, {"messageId": "309", "fix": "408", "desc": "311"}, {"messageId": "306", "fix": "409", "desc": "308"}, {"messageId": "309", "fix": "410", "desc": "311"}, {"messageId": "306", "fix": "411", "desc": "308"}, {"messageId": "309", "fix": "412", "desc": "311"}, {"messageId": "306", "fix": "413", "desc": "308"}, {"messageId": "309", "fix": "414", "desc": "311"}, "suggestUnknown", {"range": "415", "text": "416"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "417", "text": "418"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "419", "text": "416"}, {"range": "420", "text": "418"}, {"range": "421", "text": "416"}, {"range": "422", "text": "418"}, {"range": "423", "text": "416"}, {"range": "424", "text": "418"}, {"range": "425", "text": "416"}, {"range": "426", "text": "418"}, "Update the dependencies array to be: [selectedDate, selectedClass, loadStudentsWithAttendance]", {"range": "427", "text": "428"}, "Update the dependencies array to be: [students, searchTerm, attendanceFilter, filterStudents]", {"range": "429", "text": "430"}, {"range": "431", "text": "416"}, {"range": "432", "text": "418"}, "replaceWithAlt", {"alt": "433"}, {"range": "434", "text": "435"}, "Replace with `&apos;`.", {"alt": "436"}, {"range": "437", "text": "438"}, "Replace with `&lsquo;`.", {"alt": "439"}, {"range": "440", "text": "441"}, "Replace with `&#39;`.", {"alt": "442"}, {"range": "443", "text": "444"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [loadDashboardData, selectedDate]", {"range": "445", "text": "446"}, {"range": "447", "text": "416"}, {"range": "448", "text": "418"}, {"range": "449", "text": "416"}, {"range": "450", "text": "418"}, {"range": "451", "text": "416"}, {"range": "452", "text": "418"}, {"alt": "433"}, {"range": "453", "text": "454"}, {"alt": "436"}, {"range": "455", "text": "456"}, {"alt": "439"}, {"range": "457", "text": "458"}, {"alt": "442"}, {"range": "459", "text": "460"}, {"alt": "433"}, {"range": "461", "text": "462"}, {"alt": "436"}, {"range": "463", "text": "464"}, {"alt": "439"}, {"range": "465", "text": "466"}, {"alt": "442"}, {"range": "467", "text": "468"}, "Update the dependencies array to be: [currentPage, fetchPayments, filters]", {"range": "469", "text": "470"}, {"range": "471", "text": "416"}, {"range": "472", "text": "418"}, {"range": "473", "text": "416"}, {"range": "474", "text": "418"}, "Update the dependencies array to be: [fetch<PERSON><PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, showM<PERSON>h<PERSON><PERSON>er]", {"range": "475", "text": "476"}, {"alt": "433"}, {"range": "477", "text": "478"}, {"alt": "436"}, {"range": "479", "text": "480"}, {"alt": "439"}, {"range": "481", "text": "482"}, {"alt": "442"}, {"range": "483", "text": "484"}, {"alt": "433"}, {"range": "485", "text": "486"}, {"alt": "436"}, {"range": "487", "text": "488"}, {"alt": "439"}, {"range": "489", "text": "490"}, {"alt": "442"}, {"range": "491", "text": "492"}, {"alt": "433"}, {"range": "493", "text": "494"}, {"alt": "436"}, {"range": "495", "text": "496"}, {"alt": "439"}, {"range": "497", "text": "498"}, {"alt": "442"}, {"range": "499", "text": "500"}, {"alt": "433"}, {"range": "501", "text": "502"}, {"alt": "436"}, {"range": "503", "text": "504"}, {"alt": "439"}, {"range": "505", "text": "506"}, {"alt": "442"}, {"range": "507", "text": "508"}, {"range": "509", "text": "416"}, {"range": "510", "text": "418"}, {"range": "511", "text": "416"}, {"range": "512", "text": "418"}, {"range": "513", "text": "416"}, {"range": "514", "text": "418"}, {"range": "515", "text": "416"}, {"range": "516", "text": "418"}, {"range": "517", "text": "416"}, {"range": "518", "text": "418"}, {"range": "519", "text": "416"}, {"range": "520", "text": "418"}, [3050, 3053], "unknown", [3050, 3053], "never", [2513, 2516], [2513, 2516], [2805, 2808], [2805, 2808], [2958, 2961], [2958, 2961], [2966, 2969], [2966, 2969], [1928, 1957], "[selectedDate, selectedClass, loadStudentsWithAttendance]", [2070, 2110], "[students, searchTerm, attendanceFilter, filterStudents]", [12773, 12776], [12773, 12776], "&apos;", [25612, 25654], " absent students&apos; parents\n                ", "&lsquo;", [25612, 25654], " absent students&lsquo; parents\n                ", "&#39;", [25612, 25654], " absent students&#39; parents\n                ", "&rsquo;", [25612, 25654], " absent students&rsquo; parents\n                ", [1324, 1338], "[loadDashboardData, selectedDate]", [2061, 2064], [2061, 2064], [2171, 2174], [2171, 2174], [2395, 2398], [2395, 2398], [8924, 8942], "Today&apos;s Attendance", [8924, 8942], "Today&lsquo;s Attendance", [8924, 8942], "Today&#39;s Attendance", [8924, 8942], "Today&rsquo;s Attendance", [15330, 15355], "Record today&apos;s attendance", [15330, 15355], "Record today&lsquo;s attendance", [15330, 15355], "Record today&#39;s attendance", [15330, 15355], "Record today&rsquo;s attendance", [2360, 2382], "[currentPage, fetchPayments, filters]", [15409, 15412], [15409, 15412], [16411, 16414], [16411, 16414], [2214, 2260], "[fetchP<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, show<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]", [5697, 5711], "Father&apos;s Name:", [5697, 5711], "Father&lsquo;s Name:", [5697, 5711], "Father&#39;s Name:", [5697, 5711], "Father&rsquo;s Name:", [6014, 6028], "Mother&apos;s Name:", [6014, 6028], "Mother&lsquo;s Name:", [6014, 6028], "Mother&#39;s Name:", [6014, 6028], "Mother&rsquo;s Name:", [6253, 6269], "Father&apos;s Mobile:", [6253, 6269], "Father&lsquo;s Mobile:", [6253, 6269], "Father&#39;s Mobile:", [6253, 6269], "Father&rsquo;s Mobile:", [6493, 6509], "Mother&apos;s Mobile:", [6493, 6509], "Mother&lsquo;s Mobile:", [6493, 6509], "Mother&#39;s Mobile:", [6493, 6509], "Mother&rsquo;s Mobile:", [5257, 5260], [5257, 5260], [7211, 7214], [7211, 7214], [7354, 7357], [7354, 7357], [7503, 7506], [7503, 7506], [7511, 7514], [7511, 7514], [7751, 7754], [7751, 7754]]