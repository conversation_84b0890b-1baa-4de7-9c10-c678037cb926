[{"/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/bulk/route.ts": "1", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/messages/route.ts": "2", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/route.ts": "3", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/statistics/route.ts": "4", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/students/route.ts": "5", "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/trends/route.ts": "6", "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes/route.ts": "7", "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes-with-names/route.ts": "8", "/home/<USER>/school/first-step-school-fee-management/src/app/api/fee-history/route.ts": "9", "/home/<USER>/school/first-step-school-fee-management/src/app/api/payments/route.ts": "10", "/home/<USER>/school/first-step-school-fee-management/src/app/api/pending-fees/route.ts": "11", "/home/<USER>/school/first-step-school-fee-management/src/app/api/students/route.ts": "12", "/home/<USER>/school/first-step-school-fee-management/src/app/api/test-pending/route.ts": "13", "/home/<USER>/school/first-step-school-fee-management/src/app/layout.tsx": "14", "/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx": "15", "/home/<USER>/school/first-step-school-fee-management/src/app/page.tsx": "16", "/home/<USER>/school/first-step-school-fee-management/src/app/receipt/[id]/page.tsx": "17", "/home/<USER>/school/first-step-school-fee-management/src/components/AttendanceManagement.tsx": "18", "/home/<USER>/school/first-step-school-fee-management/src/components/DashboardAnalytics.tsx": "19", "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementDashboard.tsx": "20", "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementForm.tsx": "21", "/home/<USER>/school/first-step-school-fee-management/src/components/FeeRecordsComponent.tsx": "22", "/home/<USER>/school/first-step-school-fee-management/src/components/PendingFeesComponent.tsx": "23", "/home/<USER>/school/first-step-school-fee-management/src/components/ReceiptComponent.tsx": "24", "/home/<USER>/school/first-step-school-fee-management/src/lib/database.ts": "25", "/home/<USER>/school/first-step-school-fee-management/src/lib/supabase.ts": "26", "/home/<USER>/school/first-step-school-fee-management/src/types/database.ts": "27"}, {"size": 1639, "mtime": 1751280746396, "results": "28", "hashOfConfig": "29"}, {"size": 1914, "mtime": 1751280781165, "results": "30", "hashOfConfig": "29"}, {"size": 1824, "mtime": 1751280736812, "results": "31", "hashOfConfig": "29"}, {"size": 710, "mtime": 1751280762141, "results": "32", "hashOfConfig": "29"}, {"size": 758, "mtime": 1751280753721, "results": "33", "hashOfConfig": "29"}, {"size": 782, "mtime": 1751280769753, "results": "34", "hashOfConfig": "29"}, {"size": 635, "mtime": 1751271543424, "results": "35", "hashOfConfig": "29"}, {"size": 538, "mtime": 1751272032013, "results": "36", "hashOfConfig": "29"}, {"size": 882, "mtime": 1751276621194, "results": "37", "hashOfConfig": "29"}, {"size": 6726, "mtime": 1751276607946, "results": "38", "hashOfConfig": "29"}, {"size": 4226, "mtime": 1751281595677, "results": "39", "hashOfConfig": "29"}, {"size": 799, "mtime": 1751271560412, "results": "40", "hashOfConfig": "29"}, {"size": 4049, "mtime": 1751280125281, "results": "41", "hashOfConfig": "29"}, {"size": 607, "mtime": 1751281108723, "results": "42", "hashOfConfig": "29"}, {"size": 744, "mtime": 1751281617009, "results": "43", "hashOfConfig": "29"}, {"size": 215, "mtime": 1751272539467, "results": "44", "hashOfConfig": "29"}, {"size": 1504, "mtime": 1751271682540, "results": "45", "hashOfConfig": "29"}, {"size": 16554, "mtime": 1751280885550, "results": "46", "hashOfConfig": "29"}, {"size": 16478, "mtime": 1751281421205, "results": "47", "hashOfConfig": "29"}, {"size": 4407, "mtime": 1751281052075, "results": "48", "hashOfConfig": "29"}, {"size": 12957, "mtime": 1751272861234, "results": "49", "hashOfConfig": "29"}, {"size": 27610, "mtime": 1751276903004, "results": "50", "hashOfConfig": "29"}, {"size": 15180, "mtime": 1751277782291, "results": "51", "hashOfConfig": "29"}, {"size": 8366, "mtime": 1751272162817, "results": "52", "hashOfConfig": "29"}, {"size": 18158, "mtime": 1751280710028, "results": "53", "hashOfConfig": "29"}, {"size": 753, "mtime": 1751271526940, "results": "54", "hashOfConfig": "29"}, {"size": 2762, "mtime": 1751280585428, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1187ui9", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/bulk/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/messages/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/statistics/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/students/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/attendance/trends/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/classes-with-names/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/fee-history/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/payments/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/pending-fees/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/students/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/api/test-pending/route.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/layout.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/not-found.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/page.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/app/receipt/[id]/page.tsx", ["137", "138"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/AttendanceManagement.tsx", ["139", "140"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/DashboardAnalytics.tsx", ["141", "142", "143", "144", "145", "146", "147", "148", "149"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementDashboard.tsx", ["150"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/FeeManagementForm.tsx", [], [], "/home/<USER>/school/first-step-school-fee-management/src/components/FeeRecordsComponent.tsx", ["151", "152", "153", "154"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/PendingFeesComponent.tsx", ["155", "156", "157"], [], "/home/<USER>/school/first-step-school-fee-management/src/components/ReceiptComponent.tsx", ["158", "159", "160", "161", "162"], [], "/home/<USER>/school/first-step-school-fee-management/src/lib/database.ts", ["163", "164", "165", "166", "167", "168"], [], "/home/<USER>/school/first-step-school-fee-management/src/lib/supabase.ts", [], [], "/home/<USER>/school/first-step-school-fee-management/src/types/database.ts", [], [], {"ruleId": "169", "severity": 2, "message": "170", "line": 2, "column": 10, "nodeType": null, "messageId": "171", "endLine": 2, "endColumn": 16}, {"ruleId": "169", "severity": 2, "message": "172", "line": 53, "column": 12, "nodeType": null, "messageId": "171", "endLine": 53, "endColumn": 17}, {"ruleId": "173", "severity": 1, "message": "174", "line": 33, "column": 6, "nodeType": "175", "endLine": 33, "endColumn": 20, "suggestions": "176"}, {"ruleId": "177", "severity": 2, "message": "178", "line": 421, "column": 59, "nodeType": "179", "messageId": "180", "suggestions": "181"}, {"ruleId": "169", "severity": 2, "message": "182", "line": 20, "column": 3, "nodeType": null, "messageId": "171", "endLine": 20, "endColumn": 11}, {"ruleId": "169", "severity": 2, "message": "183", "line": 21, "column": 3, "nodeType": null, "messageId": "171", "endLine": 21, "endColumn": 6}, {"ruleId": "169", "severity": 2, "message": "184", "line": 54, "column": 7, "nodeType": null, "messageId": "171", "endLine": 54, "endColumn": 13}, {"ruleId": "173", "severity": 1, "message": "185", "line": 65, "column": 6, "nodeType": "175", "endLine": 65, "endColumn": 20, "suggestions": "186"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 84, "column": 70, "nodeType": "189", "messageId": "190", "endLine": 84, "endColumn": 73, "suggestions": "191"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 85, "column": 68, "nodeType": "189", "messageId": "190", "endLine": 85, "endColumn": 71, "suggestions": "192"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 91, "column": 57, "nodeType": "189", "messageId": "190", "endLine": 91, "endColumn": 60, "suggestions": "193"}, {"ruleId": "177", "severity": 2, "message": "178", "line": 263, "column": 70, "nodeType": "179", "messageId": "180", "suggestions": "194"}, {"ruleId": "177", "severity": 2, "message": "178", "line": 413, "column": 62, "nodeType": "179", "messageId": "180", "suggestions": "195"}, {"ruleId": "169", "severity": 2, "message": "196", "line": 4, "column": 39, "nodeType": null, "messageId": "171", "endLine": 4, "endColumn": 44}, {"ruleId": "169", "severity": 2, "message": "197", "line": 5, "column": 10, "nodeType": null, "messageId": "171", "endLine": 5, "endColumn": 16}, {"ruleId": "173", "severity": 1, "message": "198", "line": 69, "column": 6, "nodeType": "175", "endLine": 69, "endColumn": 28, "suggestions": "199"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 377, "column": 112, "nodeType": "189", "messageId": "190", "endLine": 377, "endColumn": 115, "suggestions": "200"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 394, "column": 112, "nodeType": "189", "messageId": "190", "endLine": 394, "endColumn": 115, "suggestions": "201"}, {"ruleId": "169", "severity": 2, "message": "202", "line": 5, "column": 43, "nodeType": null, "messageId": "171", "endLine": 5, "endColumn": 49}, {"ruleId": "169", "severity": 2, "message": "203", "line": 6, "column": 10, "nodeType": null, "messageId": "171", "endLine": 6, "endColumn": 20}, {"ruleId": "173", "severity": 1, "message": "204", "line": 66, "column": 6, "nodeType": "175", "endLine": 66, "endColumn": 52, "suggestions": "205"}, {"ruleId": "169", "severity": 2, "message": "206", "line": 5, "column": 19, "nodeType": null, "messageId": "171", "endLine": 5, "endColumn": 27}, {"ruleId": "177", "severity": 2, "message": "178", "line": 138, "column": 55, "nodeType": "179", "messageId": "180", "suggestions": "207"}, {"ruleId": "177", "severity": 2, "message": "178", "line": 144, "column": 55, "nodeType": "179", "messageId": "180", "suggestions": "208"}, {"ruleId": "177", "severity": 2, "message": "178", "line": 148, "column": 55, "nodeType": "179", "messageId": "180", "suggestions": "209"}, {"ruleId": "177", "severity": 2, "message": "178", "line": 152, "column": 55, "nodeType": "179", "messageId": "180", "suggestions": "210"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 192, "column": 61, "nodeType": "189", "messageId": "190", "endLine": 192, "endColumn": 64, "suggestions": "211"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 248, "column": 71, "nodeType": "189", "messageId": "190", "endLine": 248, "endColumn": 74, "suggestions": "212"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 251, "column": 74, "nodeType": "189", "messageId": "190", "endLine": 251, "endColumn": 77, "suggestions": "213"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 255, "column": 52, "nodeType": "189", "messageId": "190", "endLine": 255, "endColumn": 55, "suggestions": "214"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 255, "column": 60, "nodeType": "189", "messageId": "190", "endLine": 255, "endColumn": 63, "suggestions": "215"}, {"ruleId": "187", "severity": 2, "message": "188", "line": 260, "column": 66, "nodeType": "189", "messageId": "190", "endLine": 260, "endColumn": 69, "suggestions": "216"}, "@typescript-eslint/no-unused-vars", "'format' is defined but never used.", "unusedVar", "'error' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadStudentsWithAttendance'. Either include it or remove the dependency array.", "ArrayExpression", ["217"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["218", "219", "220", "221"], "'BarChart' is defined but never used.", "'Bar' is defined but never used.", "'COLORS' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", ["222"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["223", "224"], ["225", "226"], ["227", "228"], ["229", "230", "231", "232"], ["233", "234", "235", "236"], "'Users' is defined but never used.", "'Search' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPayments'. Either include it or remove the dependency array.", ["237"], ["238", "239"], ["240", "241"], "'Filter' is defined but never used.", "'FeePayment' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPendingFees'. Either include it or remove the dependency array.", ["242"], "'Download' is defined but never used.", ["243", "244", "245", "246"], ["247", "248", "249", "250"], ["251", "252", "253", "254"], ["255", "256", "257", "258"], ["259", "260"], ["261", "262"], ["263", "264"], ["265", "266"], ["267", "268"], ["269", "270"], {"desc": "271", "fix": "272"}, {"messageId": "273", "data": "274", "fix": "275", "desc": "276"}, {"messageId": "273", "data": "277", "fix": "278", "desc": "279"}, {"messageId": "273", "data": "280", "fix": "281", "desc": "282"}, {"messageId": "273", "data": "283", "fix": "284", "desc": "285"}, {"desc": "286", "fix": "287"}, {"messageId": "288", "fix": "289", "desc": "290"}, {"messageId": "291", "fix": "292", "desc": "293"}, {"messageId": "288", "fix": "294", "desc": "290"}, {"messageId": "291", "fix": "295", "desc": "293"}, {"messageId": "288", "fix": "296", "desc": "290"}, {"messageId": "291", "fix": "297", "desc": "293"}, {"messageId": "273", "data": "298", "fix": "299", "desc": "276"}, {"messageId": "273", "data": "300", "fix": "301", "desc": "279"}, {"messageId": "273", "data": "302", "fix": "303", "desc": "282"}, {"messageId": "273", "data": "304", "fix": "305", "desc": "285"}, {"messageId": "273", "data": "306", "fix": "307", "desc": "276"}, {"messageId": "273", "data": "308", "fix": "309", "desc": "279"}, {"messageId": "273", "data": "310", "fix": "311", "desc": "282"}, {"messageId": "273", "data": "312", "fix": "313", "desc": "285"}, {"desc": "314", "fix": "315"}, {"messageId": "288", "fix": "316", "desc": "290"}, {"messageId": "291", "fix": "317", "desc": "293"}, {"messageId": "288", "fix": "318", "desc": "290"}, {"messageId": "291", "fix": "319", "desc": "293"}, {"desc": "320", "fix": "321"}, {"messageId": "273", "data": "322", "fix": "323", "desc": "276"}, {"messageId": "273", "data": "324", "fix": "325", "desc": "279"}, {"messageId": "273", "data": "326", "fix": "327", "desc": "282"}, {"messageId": "273", "data": "328", "fix": "329", "desc": "285"}, {"messageId": "273", "data": "330", "fix": "331", "desc": "276"}, {"messageId": "273", "data": "332", "fix": "333", "desc": "279"}, {"messageId": "273", "data": "334", "fix": "335", "desc": "282"}, {"messageId": "273", "data": "336", "fix": "337", "desc": "285"}, {"messageId": "273", "data": "338", "fix": "339", "desc": "276"}, {"messageId": "273", "data": "340", "fix": "341", "desc": "279"}, {"messageId": "273", "data": "342", "fix": "343", "desc": "282"}, {"messageId": "273", "data": "344", "fix": "345", "desc": "285"}, {"messageId": "273", "data": "346", "fix": "347", "desc": "276"}, {"messageId": "273", "data": "348", "fix": "349", "desc": "279"}, {"messageId": "273", "data": "350", "fix": "351", "desc": "282"}, {"messageId": "273", "data": "352", "fix": "353", "desc": "285"}, {"messageId": "288", "fix": "354", "desc": "290"}, {"messageId": "291", "fix": "355", "desc": "293"}, {"messageId": "288", "fix": "356", "desc": "290"}, {"messageId": "291", "fix": "357", "desc": "293"}, {"messageId": "288", "fix": "358", "desc": "290"}, {"messageId": "291", "fix": "359", "desc": "293"}, {"messageId": "288", "fix": "360", "desc": "290"}, {"messageId": "291", "fix": "361", "desc": "293"}, {"messageId": "288", "fix": "362", "desc": "290"}, {"messageId": "291", "fix": "363", "desc": "293"}, {"messageId": "288", "fix": "364", "desc": "290"}, {"messageId": "291", "fix": "365", "desc": "293"}, "Update the dependencies array to be: [loadStudentsWithAttendance, selectedDate]", {"range": "366", "text": "367"}, "replaceWithAlt", {"alt": "368"}, {"range": "369", "text": "370"}, "Replace with `&apos;`.", {"alt": "371"}, {"range": "372", "text": "373"}, "Replace with `&lsquo;`.", {"alt": "374"}, {"range": "375", "text": "376"}, "Replace with `&#39;`.", {"alt": "377"}, {"range": "378", "text": "379"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [loadDashboardData, selectedDate]", {"range": "380", "text": "381"}, "suggestUnknown", {"range": "382", "text": "383"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "384", "text": "385"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "386", "text": "383"}, {"range": "387", "text": "385"}, {"range": "388", "text": "383"}, {"range": "389", "text": "385"}, {"alt": "368"}, {"range": "390", "text": "391"}, {"alt": "371"}, {"range": "392", "text": "393"}, {"alt": "374"}, {"range": "394", "text": "395"}, {"alt": "377"}, {"range": "396", "text": "397"}, {"alt": "368"}, {"range": "398", "text": "399"}, {"alt": "371"}, {"range": "400", "text": "401"}, {"alt": "374"}, {"range": "402", "text": "403"}, {"alt": "377"}, {"range": "404", "text": "405"}, "Update the dependencies array to be: [currentPage, fetchPayments, filters]", {"range": "406", "text": "407"}, {"range": "408", "text": "383"}, {"range": "409", "text": "385"}, {"range": "410", "text": "383"}, {"range": "411", "text": "385"}, "Update the dependencies array to be: [fetch<PERSON><PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, showM<PERSON>h<PERSON><PERSON>er]", {"range": "412", "text": "413"}, {"alt": "368"}, {"range": "414", "text": "415"}, {"alt": "371"}, {"range": "416", "text": "417"}, {"alt": "374"}, {"range": "418", "text": "419"}, {"alt": "377"}, {"range": "420", "text": "421"}, {"alt": "368"}, {"range": "422", "text": "423"}, {"alt": "371"}, {"range": "424", "text": "425"}, {"alt": "374"}, {"range": "426", "text": "427"}, {"alt": "377"}, {"range": "428", "text": "429"}, {"alt": "368"}, {"range": "430", "text": "431"}, {"alt": "371"}, {"range": "432", "text": "433"}, {"alt": "374"}, {"range": "434", "text": "435"}, {"alt": "377"}, {"range": "436", "text": "437"}, {"alt": "368"}, {"range": "438", "text": "439"}, {"alt": "371"}, {"range": "440", "text": "441"}, {"alt": "374"}, {"range": "442", "text": "443"}, {"alt": "377"}, {"range": "444", "text": "445"}, {"range": "446", "text": "383"}, {"range": "447", "text": "385"}, {"range": "448", "text": "383"}, {"range": "449", "text": "385"}, {"range": "450", "text": "383"}, {"range": "451", "text": "385"}, {"range": "452", "text": "383"}, {"range": "453", "text": "385"}, {"range": "454", "text": "383"}, {"range": "455", "text": "385"}, {"range": "456", "text": "383"}, {"range": "457", "text": "385"}, [1128, 1142], "[loadStudentsWithAttendance, selectedDate]", "&apos;", [15985, 16025], " absent students&apos; parents\n              ", "&lsquo;", [15985, 16025], " absent students&lsquo; parents\n              ", "&#39;", [15985, 16025], " absent students&#39; parents\n              ", "&rsquo;", [15985, 16025], " absent students&rsquo; parents\n              ", [1324, 1338], "[loadDashboardData, selectedDate]", [2061, 2064], "unknown", [2061, 2064], "never", [2171, 2174], [2171, 2174], [2395, 2398], [2395, 2398], [8924, 8942], "Today&apos;s Attendance", [8924, 8942], "Today&lsquo;s Attendance", [8924, 8942], "Today&#39;s Attendance", [8924, 8942], "Today&rsquo;s Attendance", [15330, 15355], "Record today&apos;s attendance", [15330, 15355], "Record today&lsquo;s attendance", [15330, 15355], "Record today&#39;s attendance", [15330, 15355], "Record today&rsquo;s attendance", [2360, 2382], "[currentPage, fetchPayments, filters]", [15409, 15412], [15409, 15412], [16411, 16414], [16411, 16414], [2214, 2260], "[fetchP<PERSON><PERSON><PERSON>, selected<PERSON><PERSON><PERSON>, selected<PERSON><PERSON>, show<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>]", [5346, 5360], "Father&apos;s Name:", [5346, 5360], "Father&lsquo;s Name:", [5346, 5360], "Father&#39;s Name:", [5346, 5360], "Father&rsquo;s Name:", [5632, 5646], "Mother&apos;s Name:", [5632, 5646], "Mother&lsquo;s Name:", [5632, 5646], "Mother&#39;s Name:", [5632, 5646], "Mother&rsquo;s Name:", [5859, 5875], "Father&apos;s Mobile:", [5859, 5875], "Father&lsquo;s Mobile:", [5859, 5875], "Father&#39;s Mobile:", [5859, 5875], "Father&rsquo;s Mobile:", [6099, 6115], "Mother&apos;s Mobile:", [6099, 6115], "Mother&lsquo;s Mobile:", [6099, 6115], "Mother&#39;s Mobile:", [6099, 6115], "Mother&rsquo;s Mobile:", [5257, 5260], [5257, 5260], [7211, 7214], [7211, 7214], [7354, 7357], [7354, 7357], [7503, 7506], [7503, 7506], [7511, 7514], [7511, 7514], [7751, 7754], [7751, 7754]]