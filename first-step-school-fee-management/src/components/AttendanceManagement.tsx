'use client'

import { useState, useEffect } from 'react'
import { format } from 'date-fns'
import { Calendar, Users, MessageSquare, CheckCircle, XCircle, Save, Send } from 'lucide-react'
import DatePicker from 'react-datepicker'
import { Student, Attendance } from '@/types/database'
import "react-datepicker/dist/react-datepicker.css"

interface StudentWithAttendance extends Student {
  attendance?: Attendance
}

interface AttendanceStats {
  totalStudents: number
  presentCount: number
  absentCount: number
  attendancePercentage: number
}

export default function AttendanceManagement() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const [students, setStudents] = useState<StudentWithAttendance[]>([])
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [stats, setStats] = useState<AttendanceStats | null>(null)
  const [messageText, setMessageText] = useState('')
  const [sendingMessages, setSendingMessages] = useState(false)

  // Load students with attendance for selected date
  useEffect(() => {
    loadStudentsWithAttendance()
  }, [selectedDate])

  const loadStudentsWithAttendance = async () => {
    setLoading(true)
    try {
      const dateStr = format(selectedDate, 'yyyy-MM-dd')
      const response = await fetch(`/api/attendance/students?date=${dateStr}`)
      if (!response.ok) throw new Error('Failed to fetch students')
      
      const studentsData = await response.json()
      setStudents(studentsData)

      // Load statistics
      const statsResponse = await fetch(`/api/attendance/statistics?date=${dateStr}`)
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      }
    } catch (error) {
      console.error('Error loading students:', error)
      alert('Failed to load students')
    } finally {
      setLoading(false)
    }
  }

  const handleAttendanceChange = (studentId: string, status: 'present' | 'absent') => {
    setStudents(prev => prev.map(student => {
      if (student.id === studentId) {
        return {
          ...student,
          attendance: {
            ...student.attendance,
            student_id: studentId,
            attendance_date: format(selectedDate, 'yyyy-MM-dd'),
            status,
            marked_by: 'admin',
            id: student.attendance?.id || '',
            created_at: student.attendance?.created_at || '',
            updated_at: student.attendance?.updated_at || ''
          }
        }
      }
      return student
    }))
  }

  const markAllPresent = () => {
    setStudents(prev => prev.map(student => ({
      ...student,
      attendance: {
        ...student.attendance,
        student_id: student.id,
        attendance_date: format(selectedDate, 'yyyy-MM-dd'),
        status: 'present' as const,
        marked_by: 'admin',
        id: student.attendance?.id || '',
        created_at: student.attendance?.created_at || '',
        updated_at: student.attendance?.updated_at || ''
      }
    })))
  }

  const markAllAbsent = () => {
    setStudents(prev => prev.map(student => ({
      ...student,
      attendance: {
        ...student.attendance,
        student_id: student.id,
        attendance_date: format(selectedDate, 'yyyy-MM-dd'),
        status: 'absent' as const,
        marked_by: 'admin',
        id: student.attendance?.id || '',
        created_at: student.attendance?.created_at || '',
        updated_at: student.attendance?.updated_at || ''
      }
    })))
  }

  const saveAttendance = async () => {
    setSaving(true)
    try {
      const attendanceList = students.map(student => ({
        student_id: student.id,
        attendance_date: format(selectedDate, 'yyyy-MM-dd'),
        status: student.attendance?.status || 'absent',
        marked_by: 'admin'
      }))

      const response = await fetch('/api/attendance/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ attendanceList }),
      })

      if (!response.ok) throw new Error('Failed to save attendance')

      alert('Attendance saved successfully!')
      await loadStudentsWithAttendance() // Reload to get updated data
    } catch (error) {
      console.error('Error saving attendance:', error)
      alert('Failed to save attendance')
    } finally {
      setSaving(false)
    }
  }

  const sendMessagesToAbsentStudents = async () => {
    if (!messageText.trim()) {
      alert('Please enter a message to send')
      return
    }

    setSendingMessages(true)
    try {
      const absentStudents = students.filter(student => student.attendance?.status === 'absent')
      
      if (absentStudents.length === 0) {
        alert('No absent students to send messages to')
        return
      }

      const messages = []
      for (const student of absentStudents) {
        // Send to father if available
        if (student.father_mobile) {
          messages.push({
            student_id: student.id,
            attendance_date: format(selectedDate, 'yyyy-MM-dd'),
            message_content: messageText,
            recipient_type: 'father',
            recipient_number: student.father_mobile
          })
        }
        
        // Send to mother if available and different from father
        if (student.mother_mobile && student.mother_mobile !== student.father_mobile) {
          messages.push({
            student_id: student.id,
            attendance_date: format(selectedDate, 'yyyy-MM-dd'),
            message_content: messageText,
            recipient_type: 'mother',
            recipient_number: student.mother_mobile
          })
        }
      }

      // Save messages (in a real app, you'd integrate with SMS service)
      for (const message of messages) {
        await fetch('/api/attendance/messages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(message),
        })
      }

      alert(`Messages queued for ${absentStudents.length} absent students`)
      setMessageText('')
    } catch (error) {
      console.error('Error sending messages:', error)
      alert('Failed to send messages')
    } finally {
      setSendingMessages(false)
    }
  }

  const presentCount = students.filter(s => s.attendance?.status === 'present').length
  const absentCount = students.filter(s => s.attendance?.status === 'absent').length
  const unmarkedCount = students.filter(s => !s.attendance?.status).length

  return (
    <div className="space-y-6">
      {/* Header with Date Selection */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Attendance Management</h2>
            <p className="text-gray-600">Mark attendance for students</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-gray-500" />
            <DatePicker
              selected={selectedDate}
              onChange={(date: Date) => setSelectedDate(date)}
              dateFormat="yyyy-MM-dd"
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              maxDate={new Date()}
            />
          </div>
        </div>
      </div>

      {/* Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-blue-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Students</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalStudents}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Present Today</p>
                <p className="text-2xl font-semibold text-gray-900">{presentCount}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center">
              <XCircle className="w-8 h-8 text-red-500" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Absent Today</p>
                <p className="text-2xl font-semibold text-gray-900">{absentCount}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">%</span>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Attendance Rate</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {stats.attendancePercentage.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Bulk Actions</h3>
            <p className="text-sm text-gray-600">
              {unmarkedCount > 0 && `${unmarkedCount} students not marked yet`}
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            <button
              onClick={markAllPresent}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Mark All Present
            </button>
            <button
              onClick={markAllAbsent}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Mark All Absent
            </button>
            <button
              onClick={saveAttendance}
              disabled={saving}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2"
            >
              <Save className="w-4 h-4" />
              {saving ? 'Saving...' : 'Save Attendance'}
            </button>
          </div>
        </div>
      </div>

      {/* Student List */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h3 className="text-lg font-medium text-gray-900">Students</h3>
          <p className="text-sm text-gray-600">Click to mark attendance for each student</p>
        </div>

        <div className="p-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading students...</p>
            </div>
          ) : students.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No students found</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {students.map((student) => (
                <div
                  key={student.id}
                  className={`border rounded-lg p-4 transition-colors ${
                    student.attendance?.status === 'present'
                      ? 'border-green-200 bg-green-50'
                      : student.attendance?.status === 'absent'
                      ? 'border-red-200 bg-red-50'
                      : 'border-gray-200 bg-white hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium text-gray-900">{student.student_name}</h4>
                      <p className="text-sm text-gray-600">Class: {student.class_id}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      {student.attendance?.status === 'present' && (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      )}
                      {student.attendance?.status === 'absent' && (
                        <XCircle className="w-5 h-5 text-red-500" />
                      )}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={() => handleAttendanceChange(student.id, 'present')}
                      className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        student.attendance?.status === 'present'
                          ? 'bg-green-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-green-100 hover:text-green-700'
                      }`}
                    >
                      Present
                    </button>
                    <button
                      onClick={() => handleAttendanceChange(student.id, 'absent')}
                      className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                        student.attendance?.status === 'absent'
                          ? 'bg-red-600 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-red-100 hover:text-red-700'
                      }`}
                    >
                      Absent
                    </button>
                  </div>

                  {student.attendance?.status === 'absent' && (
                    <div className="mt-2 text-xs text-gray-600">
                      <p>Father: {student.father_mobile || 'N/A'}</p>
                      <p>Mother: {student.mother_mobile || 'N/A'}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Messaging Section */}
      {absentCount > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center gap-2 mb-4">
            <MessageSquare className="w-5 h-5 text-blue-500" />
            <h3 className="text-lg font-medium text-gray-900">Send Message to Absent Students</h3>
          </div>

          <div className="space-y-4">
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                Message Content
              </label>
              <textarea
                id="message"
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter message to send to parents of absent students..."
              />
            </div>

            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-600">
                Will send to {absentCount} absent students' parents
              </p>
              <button
                onClick={sendMessagesToAbsentStudents}
                disabled={sendingMessages || !messageText.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 transition-colors flex items-center gap-2"
              >
                <Send className="w-4 h-4" />
                {sendingMessages ? 'Sending...' : 'Send Messages'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
